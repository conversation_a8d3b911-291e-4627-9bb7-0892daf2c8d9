/**
 * 主控制器模块
 * 
 * 职责: 协调各模块工作，处理页面初始化、事件绑定、模块间通信等主控制逻辑
 * 依赖: 所有其他模块
 * 作者: Device Tracking System
 * 创建时间: 2025-01-30
 */

// 导入所有模块
import { getConfig } from './config.js';
import { DOMUtils } from './utils.js';
import { EVENT_TYPES, on, emit } from './event-system.js';
import { apiClient } from './api-client.js';
import { wsClient, MESSAGE_TYPE } from './websocket-client.js';
import { deviceRenderer } from './device-renderer.js';
import { deviceModal } from './device-modal.js';
import { searchFilter } from './search-filter.js';
import { clientStateManager } from './client-state-manager.js';

// ==================== 设备追踪应用主控制器 ====================
/**
 * 设备追踪应用主控制器类
 * 协调各个模块的工作，管理应用的整体状态和生命周期
 */
export class DeviceTrackingApp {
    constructor() {
        // 应用状态
        this.state = {
            initialized: false,
            loading: false,
            error: null,
            devices: [],           // 当前页的设备数据
            totalDevices: 0,       // 总设备数（用于分页）
            stats: {},
            currentPage: 1,
            pageSize: getConfig('ui.PAGINATION.DEFAULT_PAGE_SIZE'),
            isTableInitialized: false
        };

        // 事件监听器清理函数
        this.eventCleanupFunctions = [];

        // 更新控制
        this.updateControl = {
            lastUpdateTime: 0,
            updateThrottle: getConfig('ui.REFRESH.UPDATE_THROTTLE'), // 从配置读取节流时间
            pendingUpdate: null,
            isUpdating: false
        };

        // WebSocket状态
        this.isWebSocketConnected = false;

        console.log('[DeviceTrackingApp] 主控制器已创建');
    }

    /**
     * 初始化应用
     */
    async init() {
        if (this.state.initialized) {
            console.warn('[DeviceTrackingApp] 应用已经初始化');
            return;
        }

        console.log('[DeviceTrackingApp] 开始初始化应用...');

        try {
            // 1. 绑定事件监听器
            this.bindEventListeners();

            // 2. 初始化WebSocket连接
            await this.initWebSocket();

            // 3. 加载初始数据
            await this.loadInitialData();

            // 4. 标记为已初始化
            this.state.initialized = true;

            // 5. 发送初始化完成事件
            emit(EVENT_TYPES.APP_INITIALIZED, {
                timestamp: Date.now(),
                version: getConfig('system.APP.VERSION')
            });

            console.log('[DeviceTrackingApp] 应用初始化完成');

            // 暴露到全局作用域用于调试
            window.clientStateManager = clientStateManager;

        } catch (error) {
            console.error('[DeviceTrackingApp] 应用初始化失败:', error);
            this.handleError(error);
        }
    }

    /**
     * 绑定事件监听器
     * @private
     */
    bindEventListeners() {
        // 设备列表更新事件
        const unsubscribeDeviceListUpdate = on(EVENT_TYPES.DEVICE_LIST_UPDATED, (data) => {
            this.handleDeviceListUpdate(data);
        });
        this.eventCleanupFunctions.push(unsubscribeDeviceListUpdate);

        // 过滤条件变更事件
        const unsubscribeFilterChange = on(EVENT_TYPES.FILTER_CHANGED, (data) => {
            this.handleFilterChange(data);
        });
        this.eventCleanupFunctions.push(unsubscribeFilterChange);

        // 分页变更事件
        const unsubscribePageChange = on(EVENT_TYPES.PAGE_CHANGED, (data) => {
            this.handlePageChange(data);
        });
        this.eventCleanupFunctions.push(unsubscribePageChange);

        // 设备详情请求事件
        const unsubscribeDeviceDetailRequest = on(EVENT_TYPES.DEVICE_DETAIL_REQUESTED, (data) => {
            this.handleDeviceDetailRequest(data);
        });
        this.eventCleanupFunctions.push(unsubscribeDeviceDetailRequest);

        // WebSocket连接状态事件
        const unsubscribeWSConnected = on(EVENT_TYPES.WEBSOCKET_CONNECTED, () => {
            this.handleWebSocketConnected();
        });
        this.eventCleanupFunctions.push(unsubscribeWSConnected);

        const unsubscribeWSDisconnected = on(EVENT_TYPES.WEBSOCKET_DISCONNECTED, () => {
            this.handleWebSocketDisconnected();
        });
        this.eventCleanupFunctions.push(unsubscribeWSDisconnected);

        // 错误事件
        const unsubscribeError = on(EVENT_TYPES.ERROR_OCCURRED, (data) => {
            this.handleError(data);
        });
        this.eventCleanupFunctions.push(unsubscribeError);

        // 页面卸载事件
        window.addEventListener('beforeunload', () => {
            this.destroy();
        });

        console.log('[DeviceTrackingApp] 事件监听器已绑定');
    }

    /**
     * 初始化WebSocket连接
     * @private
     */
    async initWebSocket() {
        try {
            await wsClient.connect();
            console.log('[DeviceTrackingApp] WebSocket连接已建立');
        } catch (error) {
            console.error('[DeviceTrackingApp] WebSocket连接失败:', error);
            this.handleError(new Error('WebSocket连接失败，请检查网络连接'));
        }
    }

    /**
     * 加载初始数据
     * @private
     */
    async loadInitialData() {
        this.setLoading(true);

        try {
            // 获取第一页数据，传递分页参数
            const params = {
                page: this.state.currentPage,
                page_size: this.state.pageSize
            };
            const data = await apiClient.getDeviceList(params);
            // 初始加载时强制更新IMSI选项
            this.handleDeviceListUpdate(data, true);

            console.log('[DeviceTrackingApp] 初始数据加载完成');
        } catch (error) {
            console.error('[DeviceTrackingApp] 初始数据加载失败:', error);
            throw error;
        } finally {
            this.setLoading(false);
        }
    }

    /**
     * 处理设备列表更新
     * @param {Object} data 设备列表数据
     * @param {boolean} forceUpdateImsi 是否强制更新IMSI选项（默认false）
     * @private
     */
    handleDeviceListUpdate(data, forceUpdateImsi = false) {
        // 检查是否是WebSocket实时更新
        if (data.isRealtimeUpdate) {
            // 如果表格还没有初始渲染，跳过实时更新
            if (!this.state.isTableInitialized) {
                console.log('[DeviceTrackingApp] 表格未初始化，跳过实时更新');
                return;
            }
            this.handleRealtimeDeviceUpdate(data);
            return;
        }
        console.log('[DEBUG] handleDeviceListUpdate 开始处理数据:', data);
        console.log('[DEBUG] 原始设备数据长度:', data.devices ? data.devices.length : 0);
        console.log('[DEBUG] 原始设备数据前3条:', data.devices ? data.devices.slice(0, 3) : []);

        // 检查是否需要节流更新
        const now = Date.now();
        if (now - this.updateControl.lastUpdateTime < this.updateControl.updateThrottle) {
            // 保存待更新的数据
            this.updateControl.pendingUpdate = data;
            console.log('[DeviceTrackingApp] 更新被节流，保存待更新数据');

            // 设置延迟更新
            if (!this.updateControl.isUpdating) {
                this.updateControl.isUpdating = true;
                setTimeout(() => {
                    if (this.updateControl.pendingUpdate) {
                        const pendingData = this.updateControl.pendingUpdate;
                        this.updateControl.pendingUpdate = null;
                        this.updateControl.isUpdating = false;
                        this.handleDeviceListUpdate(pendingData);
                    } else {
                        this.updateControl.isUpdating = false;
                    }
                }, this.updateControl.updateThrottle);
            }
            return;
        }

        this.updateControl.lastUpdateTime = now;
        this.state.devices = data.devices || [];
        this.state.stats = data.stats || {};
        this.state.totalDevices = data.total || 0; // 保存总数

        console.log('[DEBUG] 服务端分页数据:', {
            currentPageDevices: this.state.devices.length,
            totalDevices: this.state.totalDevices,
            currentPage: this.state.currentPage,
            pageSize: this.state.pageSize,
            stats: this.state.stats,
            firstDevice: this.state.devices[0]
        });

        // 更新IMSI下拉选项（只在初始加载或强制更新时）
        searchFilter.updateImsiOptions(this.state.devices, forceUpdateImsi);

        // 直接渲染当前页数据（无需前端过滤）
        this.renderServerPageData();

        // 更新统计信息
        deviceRenderer.updateStatistics(this.state.stats);

        // 更新最后更新时间
        DOMUtils.updateLastUpdateTime();

        // 标记表格已初始化
        this.state.isTableInitialized = true;

        console.log(`[DeviceTrackingApp] 设备列表已更新: ${this.state.devices.length} 个设备`);
        console.log('[DEBUG] 更新后的状态:', {
            currentPageDevices: this.state.devices.length,
            totalDevices: this.state.totalDevices,
            currentPage: this.state.currentPage,
            pageSize: this.state.pageSize
        });
    }

    /**
     * 渲染服务端分页数据
     * @private
     */
    renderServerPageData() {
        console.log('[DEBUG] renderServerPageData 开始渲染服务端分页数据');

        // 直接渲染当前页的设备数据（后端已经处理了分页和过滤）
        deviceRenderer.renderDeviceTable(this.state.devices);

        // 渲染分页组件（基于总数）
        deviceRenderer.renderPagination(
            this.state.totalDevices,
            this.state.currentPage,
            this.state.pageSize
        );

        console.log(`[DEBUG] 服务端分页渲染完成: 显示${this.state.devices.length}条，共${this.state.totalDevices}条`);
    }

    /**
     * 处理WebSocket实时设备更新（现在接收的就是当前页数据）
     * @param {Object} data 设备列表数据
     * @private
     */
    handleRealtimeDeviceUpdate(data) {
        console.log('[DEBUG] handleRealtimeDeviceUpdate 开始处理实时数据:', data);

        // 检查是否需要节流更新
        const now = Date.now();
        if (now - this.updateControl.lastUpdateTime < this.updateControl.updateThrottle) {
            // 保存待更新的数据
            this.updateControl.pendingUpdate = data;
            console.log('[DeviceTrackingApp] 实时更新被节流，保存待更新数据');

            // 设置延迟更新
            if (!this.updateControl.isUpdating) {
                this.updateControl.isUpdating = true;
                setTimeout(() => {
                    if (this.updateControl.pendingUpdate) {
                        const pendingData = this.updateControl.pendingUpdate;
                        this.updateControl.pendingUpdate = null;
                        this.updateControl.isUpdating = false;
                        this.handleRealtimeDeviceUpdate(pendingData);
                    } else {
                        this.updateControl.isUpdating = false;
                    }
                }, this.updateControl.updateThrottle);
            }
            return;
        }

        this.updateControl.lastUpdateTime = now;

        // 更新状态（后端已经根据客户端状态推送了对应页面的数据）
        this.state.devices = data.devices || [];
        this.state.stats = data.stats || {};
        this.state.totalDevices = data.total || 0;

        // 验证页面状态是否一致
        if (data.current_page && data.current_page !== this.state.currentPage) {
            console.log(`[DEBUG] 页面状态不一致，后端: ${data.current_page}, 前端: ${this.state.currentPage}`);
            this.state.currentPage = data.current_page;
        }

        // 设置当前会话ID
        if (data.session_id) {
            clientStateManager.setCurrentSessionId(data.session_id);
        }

        console.log('[DEBUG] 实时更新设备数据:', {
            deviceCount: this.state.devices.length,
            stats: this.state.stats,
            totalDevices: this.state.totalDevices,
            currentPage: this.state.currentPage,
            sessionId: data.session_id
        });

        // 直接渲染设备表格（数据已经是当前页的）
        deviceRenderer.renderDeviceTable(this.state.devices);

        // 更新统计信息
        deviceRenderer.updateStatistics(this.state.stats);

        // 更新最后更新时间
        DOMUtils.updateLastUpdateTime();

        console.log(`[DeviceTrackingApp] 实时更新完成: ${this.state.devices.length} 个设备，共${this.state.totalDevices}个`);
    }

    /**
     * 更新当前页面显示的设备行（不重新渲染分页组件）
     * 注意：在服务端分页模式下，实时更新只更新当前页显示的设备数据
     * @private
     */
    updateCurrentPageDeviceRows() {
        console.log('[DEBUG] updateCurrentPageDeviceRows: 服务端分页模式，只更新当前页显示的设备');

        // 在服务端分页模式下，this.state.devices 已经是当前页的数据
        // 不需要再进行前端过滤和分页切片
        const currentPageDevices = this.state.devices;

        console.log('[DEBUG] updateCurrentPageDeviceRows:', {
            totalDevices: this.state.totalDevices,
            currentPageDevices: currentPageDevices.length,
            currentPage: this.state.currentPage,
            pageSize: this.state.pageSize
        });

        // 只更新当前页面的设备行，不重新渲染分页组件
        deviceRenderer.updateExistingRows(currentPageDevices);
    }

    /**
     * 处理过滤条件变更
     * @param {Object} data 过滤变更数据
     * @private
     */
    async handleFilterChange(data) {
        console.log(`[DeviceTrackingApp] 过滤条件已变更: ${data.type}`);

        // 重置到第一页
        this.state.currentPage = 1;

        // 重新请求第一页数据（包含过滤条件）
        await this.loadPageData();
    }

    /**
     * 处理分页变更
     * @param {Object} data 分页变更数据
     * @private
     */
    async handlePageChange(data) {
        this.state.currentPage = data.page;
        this.state.pageSize = data.pageSize;

        console.log(`[DeviceTrackingApp] 分页已变更: 第${data.page}页，每页${data.pageSize}条`);

        // 通知后端更新客户端状态
        this.updateClientState(data.page, data.pageSize);

        // 重新请求对应页的数据
        await this.loadPageData();
    }

    /**
     * 加载指定页的数据
     * @private
     */
    async loadPageData() {
        this.setLoading(true);

        try {
            // 构建请求参数
            const params = {
                page: this.state.currentPage,
                page_size: this.state.pageSize
            };

            // 添加过滤条件
            const filters = searchFilter.getCurrentFilters();
            if (filters.search) params.imsi = filters.search;
            if (filters.status) params.status = filters.status;
            if (filters.dateRange.start) params.start_time = filters.dateRange.start.toISOString();
            if (filters.dateRange.end) params.end_time = filters.dateRange.end.toISOString();

            console.log('[DeviceTrackingApp] 请求页面数据:', params);

            const data = await apiClient.getDeviceList(params);
            this.handleDeviceListUpdate(data, false);

            console.log(`[DeviceTrackingApp] 页面数据加载完成: 第${this.state.currentPage}页`);
        } catch (error) {
            console.error('[DeviceTrackingApp] 页面数据加载失败:', error);
            this.handleError(error);
        } finally {
            this.setLoading(false);
        }
    }

    /**
     * 处理设备详情请求
     * @param {Object} data 设备详情请求数据
     * @private
     */
    async handleDeviceDetailRequest(data) {
        try {
            // 验证IMSI参数
            if (!data || !data.imsi || data.imsi.trim() === '') {
                console.warn('[DeviceTrackingApp] IMSI参数无效:', data);
                DOMUtils.showError('设备IMSI无效，无法获取详情');
                return;
            }

            const device = await apiClient.getDeviceDetail(data.imsi);
            deviceModal.show(device);
        } catch (error) {
            console.error('[DeviceTrackingApp] 获取设备详情失败:', error);
            DOMUtils.showError(`获取设备详情失败: ${error.message}`);
        }
    }

    /**
     * 处理WebSocket连接成功
     * @private
     */
    handleWebSocketConnected() {
        console.log('[DeviceTrackingApp] WebSocket连接已建立');
        this.isWebSocketConnected = true;

        // 更新连接状态显示
        this.updateConnectionStatus(true);

        // 初始化客户端状态
        this.updateClientState(this.state.currentPage, this.state.pageSize);
    }

    /**
     * 更新客户端状态到后端
     * @param {number} page 当前页码
     * @param {number} pageSize 每页大小
     * @private
     */
    updateClientState(page, pageSize) {
        if (this.isWebSocketConnected) {
            const data = {
                page: page,
                page_size: pageSize,
                filters: {
                    status: this.state.statusFilter,
                    search: this.state.searchQuery
                }
            };

            wsClient.send(MESSAGE_TYPE.CLIENT_STATE_UPDATE, data);
            console.log(`[DeviceTrackingApp] 客户端状态已更新: 页码=${page}, 每页=${pageSize}`);
        }
    }

    /**
     * 处理WebSocket连接断开
     * @private
     */
    handleWebSocketDisconnected() {
        console.log('[DeviceTrackingApp] WebSocket连接已断开');
        this.isWebSocketConnected = false;

        // 更新连接状态显示
        this.updateConnectionStatus(false);

        // 显示连接断开提示
        DOMUtils.showError('WebSocket连接已断开，请刷新页面重新连接');
    }

    /**
     * 处理错误
     * @param {Error|Object} error 错误对象
     * @private
     */
    handleError(error) {
        const errorMessage = error.message || error.toString();
        console.error('[DeviceTrackingApp] 应用错误:', errorMessage);
        
        this.state.error = error;
        DOMUtils.showError(errorMessage);
    }

    /**
     * 应用过滤条件并重新渲染（已废弃，服务端分页模式下不使用）
     * @deprecated 在服务端分页模式下，过滤条件通过API参数传递给后端处理
     * @private
     */
    applyFiltersAndRender() {
        console.log('[DEBUG] applyFiltersAndRender 被调用，但在服务端分页模式下已废弃');
        console.warn('[DeviceTrackingApp] applyFiltersAndRender 方法已废弃，请使用 loadPageData()');

        // 在服务端分页模式下，应该调用 loadPageData() 重新请求数据
        // 这里暂时保留兼容性，但建议重构调用方
    }



    /**
     * 更新连接状态显示
     * @param {boolean} connected 是否连接
     * @private
     */
    updateConnectionStatus(connected) {
        const statusElement = document.getElementById('connectionStatus');
        if (statusElement) {
            statusElement.className = connected ? 'badge badge-success' : 'badge badge-error';
            statusElement.textContent = connected ? '实时连接' : '连接断开';
        }
    }

    /**
     * 设置加载状态
     * @param {boolean} loading 是否加载中
     * @private
     */
    setLoading(loading) {
        this.state.loading = loading;
        DOMUtils.showLoading(loading);
    }

    // ==================== 公共方法 ====================

    /**
     * 刷新数据
     */
    async refreshData() {
        if (this.state.loading) {
            console.warn('[DeviceTrackingApp] 正在加载中，跳过刷新');
            return;
        }

        try {
            this.setLoading(true);
            const data = await apiClient.getDeviceList();

            // 强制更新，忽略节流限制
            this.forceUpdateDeviceList(data);

            DOMUtils.showSuccess('数据已刷新');
            console.log('[DeviceTrackingApp] 数据刷新完成');
        } catch (error) {
            console.error('[DeviceTrackingApp] 数据刷新失败:', error);
            this.handleError(error);
        } finally {
            this.setLoading(false);
        }
    }

    /**
     * 强制更新设备列表（忽略节流和交互状态检查）
     * @param {Object} data 设备列表数据
     */
    forceUpdateDeviceList(data) {
        console.log('[DeviceTrackingApp] 强制更新设备列表');

        // 重置更新控制状态
        this.updateControl.lastUpdateTime = 0;
        this.updateControl.pendingUpdate = null;
        this.updateControl.isUpdating = false;

        // 更新数据
        this.state.devices = data.devices || [];
        this.state.stats = data.stats || {};

        // 更新IMSI下拉选项（强制更新）
        searchFilter.updateImsiOptions(this.state.devices, true);

        // 强制渲染
        const filteredDevices = searchFilter.applyAllFilters(this.state.devices);
        const { currentPage, pageSize } = this.state;
        const startIndex = (currentPage - 1) * pageSize;
        const endIndex = startIndex + pageSize;
        const pageDevices = filteredDevices.slice(startIndex, endIndex);

        // 使用强制更新方法
        deviceRenderer.forceUpdate(pageDevices);

        // 更新统计信息
        deviceRenderer.updateStatistics(this.state.stats);

        // 更新最后更新时间
        DOMUtils.updateLastUpdateTime();
    }

    /**
     * 搜索设备
     * @param {string} searchTerm 搜索词
     */
    searchDevices(searchTerm) {
        searchFilter.setSearchTerm(searchTerm);
    }

    /**
     * 过滤设备状态
     * @param {string} status 状态值
     */
    filterByStatus(status) {
        searchFilter.setFilters({ status });
    }

    /**
     * 清除所有过滤条件
     */
    clearFilters() {
        searchFilter.clearAllFilters();
    }

    /**
     * 导出设备数据
     * @param {string} format 导出格式 ('csv', 'json', 'excel')
     */
    exportData(format = 'csv') {
        try {
            const data = this.state.filteredDevices;

            switch (format.toLowerCase()) {
                case 'csv':
                    this.exportToCSV(data);
                    break;
                case 'json':
                    this.exportToJSON(data);
                    break;
                case 'excel':
                    this.exportToExcel(data);
                    break;
                default:
                    throw new Error(`不支持的导出格式: ${format}`);
            }

            DOMUtils.showSuccess(`数据已导出为 ${format.toUpperCase()} 格式`);
        } catch (error) {
            console.error('[DeviceTrackingApp] 数据导出失败:', error);
            this.handleError(error);
        }
    }

    /**
     * 导出为CSV格式
     * @param {Array} data 数据
     * @private
     */
    exportToCSV(data) {
        const headers = ['IMSI', '设备名称', '状态', '最后心跳', '位置', '速度', '电池', '工作模式'];
        const rows = data.map(device => [
            device.imsi,
            device.device_name,
            device.current_status === 1 ? '在线' : '离线',
            new Date(device.last_heartbeat_time).toLocaleString(),
            device.position.location_name,
            device.speed >= 0 ? `${device.speed} km/h` : '未知',
            `${device.battery_level}%`,
            this.getWorkModeText(device.work_mode)
        ]);

        const csvContent = [headers, ...rows]
            .map(row => row.map(cell => `"${cell}"`).join(','))
            .join('\n');

        this.downloadFile(csvContent, 'devices.csv', 'text/csv');
    }

    /**
     * 导出为JSON格式
     * @param {Array} data 数据
     * @private
     */
    exportToJSON(data) {
        const jsonContent = JSON.stringify(data, null, 2);
        this.downloadFile(jsonContent, 'devices.json', 'application/json');
    }

    /**
     * 下载文件
     * @param {string} content 文件内容
     * @param {string} filename 文件名
     * @param {string} mimeType MIME类型
     * @private
     */
    downloadFile(content, filename, mimeType) {
        const blob = new Blob([content], { type: mimeType });
        const url = URL.createObjectURL(blob);

        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        URL.revokeObjectURL(url);
    }

    /**
     * 获取工作模式文本
     * @param {number} mode 工作模式
     * @returns {string} 工作模式文本
     * @private
     */
    getWorkModeText(mode) {
        const modes = { 0: '待机', 1: '运行', 2: '维护', 3: '故障' };
        return modes[mode] || '未知';
    }

    /**
     * 获取应用状态
     * @returns {Object} 应用状态
     */
    getState() {
        return { ...this.state };
    }

    /**
     * 检查应用是否已初始化
     * @returns {boolean} 是否已初始化
     */
    isInitialized() {
        return this.state.initialized;
    }

    /**
     * 检查应用是否正在加载
     * @returns {boolean} 是否正在加载
     */
    isLoading() {
        return this.state.loading;
    }

    /**
     * 销毁应用
     */
    destroy() {
        console.log('[DeviceTrackingApp] 开始销毁应用...');

        // 断开WebSocket连接
        wsClient.disconnect();

        // 清理事件监听器
        this.eventCleanupFunctions.forEach(cleanup => cleanup());
        this.eventCleanupFunctions = [];

        // 销毁各个模块
        searchFilter.destroy();
        clientStateManager.destroy();

        // 重置状态
        this.state = {
            initialized: false,
            loading: false,
            error: null,
            devices: [],
            filteredDevices: [],
            stats: {},
            currentPage: 1,
            pageSize: getConfig('ui.PAGINATION.DEFAULT_PAGE_SIZE'),
            isTableInitialized: false
        };

        console.log('[DeviceTrackingApp] 应用已销毁');
    }
}

// ==================== 全局应用实例 ====================
export const app = new DeviceTrackingApp();

// 将应用实例暴露到全局，供调试使用
window.deviceTrackingApp = app;
app.searchFilter = searchFilter;

// ==================== 页面加载完成后自动初始化 ====================
document.addEventListener('DOMContentLoaded', async () => {
    console.log('[DeviceTrackingApp] DOM加载完成，开始初始化应用');

    try {
        await app.init();
    } catch (error) {
        console.error('[DeviceTrackingApp] 应用初始化失败:', error);
        DOMUtils.showError('应用初始化失败，请刷新页面重试');
    }
});

// ==================== 全局函数（兼容旧代码） ====================
// 这些函数用于兼容HTML中的onclick事件
window.refreshData = () => app.refreshData();
window.clearFilters = () => app.clearFilters();
window.exportData = (format) => app.exportData(format);

// 设备详情相关函数
window.showDeviceDetail = (imsi) => {
    if (typeof imsi === 'string' && imsi.trim() !== '') {
        emit(EVENT_TYPES.DEVICE_DETAIL_REQUESTED, { imsi: imsi.trim() });
    } else if (typeof imsi === 'object' && imsi !== null) {
        deviceModal.show(imsi);
    } else {
        console.warn('[DeviceTrackingApp] showDeviceDetail: 无效的IMSI参数:', imsi);
        DOMUtils.showError('设备IMSI无效，无法显示详情');
    }
};

window.showDeviceHistory = (imsi) => {
    console.log(`[DeviceTrackingApp] 显示设备历史: ${imsi}`);
    // TODO: 实现设备历史功能
};

window.exportDeviceData = (imsi) => {
    console.log(`[DeviceTrackingApp] 导出设备数据: ${imsi}`);
    // TODO: 实现单个设备数据导出
};

// ==================== 默认导出 ====================
export default app;
