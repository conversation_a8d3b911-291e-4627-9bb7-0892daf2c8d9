/**
 * 设备渲染模块
 * 
 * 职责: 负责设备列表的DOM渲染、分页显示、表格更新等UI渲染逻辑
 * 依赖: config.js, utils.js, event-system.js
 * 作者: Device Tracking System
 * 创建时间: 2025-01-30
 */

import { UI_CONFIG } from './config.js';
import { DateUtils, StatusUtils, DataUtils, DOMUtils } from './utils.js';
import { EVENT_TYPES, emit } from './event-system.js';

// ==================== 设备渲染器类 ====================
/**
 * 设备渲染器类
 * 负责设备列表的UI渲染和更新
 */
export class DeviceRenderer {
    constructor() {
        this.currentDevices = [];
        this.currentStats = {};
        this.currentPage = 1;
        this.pageSize = UI_CONFIG.PAGINATION.DEFAULT_PAGE_SIZE;
        this.totalCount = 0;

        // DOM元素缓存
        this.elements = {
            deviceTable: null,
            deviceTableBody: null,
            pagination: null,
            statistics: null,
            emptyState: null,
            loadingState: null,
            errorState: null
        };

        // 用户交互状态跟踪
        this.userInteractionState = {
            isInteracting: false,
            openDropdowns: new Set(),
            lastInteractionTime: 0,
            interactionTimeout: null
        };

        // 渲染优化
        this.renderingState = {
            isRendering: false,
            pendingUpdate: null,
            lastRenderHash: null
        };

        this.initElements();
        this.bindEvents();
        this.initInteractionTracking();
    }

    /**
     * 初始化DOM元素引用
     * @private
     */
    initElements() {
        this.elements.deviceTable = document.getElementById('deviceTable');
        this.elements.deviceTableBody = document.getElementById('deviceTableBody');
        this.elements.pagination = document.getElementById('pagination');
        this.elements.statistics = document.getElementById('statistics');
        this.elements.emptyState = document.getElementById('emptyState');
        this.elements.loadingState = document.getElementById('loadingState');
        this.elements.errorState = document.getElementById('errorState');
    }

    /**
     * 绑定事件监听器
     * @private
     */
    bindEvents() {
        // 使用事件委托处理设备操作按钮
        if (this.elements.deviceTableBody) {
            this.elements.deviceTableBody.addEventListener('click', (e) => {
                const actionElement = e.target.closest('.device-action');
                if (actionElement) {
                    e.preventDefault();
                    e.stopPropagation();

                    const action = actionElement.dataset.action;
                    const imsi = actionElement.dataset.imsi;

                    if (!imsi) {
                        console.warn('[DeviceRenderer] IMSI为空，无法执行操作');
                        return;
                    }

                    this.handleDeviceAction(action, imsi);
                }
            });
        }
    }

    /**
     * 初始化用户交互跟踪
     * @private
     */
    initInteractionTracking() {
        // 监听下拉菜单的打开和关闭
        document.addEventListener('click', (e) => {
            const dropdown = e.target.closest('.dropdown');
            if (dropdown) {
                const dropdownButton = dropdown.querySelector('[role="button"]');
                if (dropdownButton && e.target.closest('[role="button"]')) {
                    // 用户点击了下拉菜单按钮
                    this.handleDropdownToggle(dropdown);
                }
            } else {
                // 点击了其他地方，关闭所有下拉菜单
                this.closeAllDropdowns();
            }
        });

        // 监听键盘事件（ESC键关闭下拉菜单）
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeAllDropdowns();
            }
        });

        // 监听表格滚动事件
        const tableContainer = document.querySelector('.table-container');
        if (tableContainer) {
            tableContainer.addEventListener('scroll', () => {
                this.markUserInteraction();
            });
        }
    }

    /**
     * 处理设备操作
     * @param {string} action 操作类型
     * @param {string} imsi 设备IMSI
     * @private
     */
    handleDeviceAction(action, imsi) {
        console.log(`[DeviceRenderer] 执行设备操作: ${action}, IMSI: ${imsi}`);

        switch (action) {
            case 'detail':
                emit(EVENT_TYPES.DEVICE_DETAIL_REQUESTED, { imsi });
                break;
            case 'history':
                console.log(`[DeviceRenderer] 显示设备历史: ${imsi}`);
                // TODO: 实现设备历史功能
                break;
            case 'export':
                console.log(`[DeviceRenderer] 导出设备数据: ${imsi}`);
                // TODO: 实现单个设备数据导出
                break;
            default:
                console.warn(`[DeviceRenderer] 未知操作类型: ${action}`);
        }
    }

    /**
     * 处理下拉菜单切换
     * @param {HTMLElement} dropdown 下拉菜单元素
     * @private
     */
    handleDropdownToggle(dropdown) {
        const imsi = dropdown.closest('tr')?.dataset.imsi;
        if (!imsi) return;

        const isOpen = dropdown.classList.contains('dropdown-open') ||
                      dropdown.querySelector('[tabindex="0"]:focus');

        if (isOpen) {
            this.userInteractionState.openDropdowns.delete(imsi);
        } else {
            this.userInteractionState.openDropdowns.add(imsi);
        }

        this.markUserInteraction();
        console.log(`[DeviceRenderer] 下拉菜单状态变更: ${imsi}, 打开: ${!isOpen}`);
    }

    /**
     * 关闭所有下拉菜单
     * @private
     */
    closeAllDropdowns() {
        this.userInteractionState.openDropdowns.clear();
        this.userInteractionState.isInteracting = false;

        // 移除所有下拉菜单的焦点
        const dropdowns = document.querySelectorAll('.dropdown [tabindex="0"]');
        dropdowns.forEach(element => {
            element.blur();
        });
    }

    /**
     * 标记用户正在交互
     * @private
     */
    markUserInteraction() {
        this.userInteractionState.isInteracting = true;
        this.userInteractionState.lastInteractionTime = Date.now();

        // 清除之前的超时
        if (this.userInteractionState.interactionTimeout) {
            clearTimeout(this.userInteractionState.interactionTimeout);
        }

        // 设置交互结束超时（2秒后认为用户停止交互）
        this.userInteractionState.interactionTimeout = setTimeout(() => {
            this.userInteractionState.isInteracting = false;
            console.log('[DeviceRenderer] 用户交互结束');
        }, 2000);
    }

    /**
     * 检查是否可以进行渲染更新
     * @returns {boolean} 是否可以更新
     * @private
     */
    canUpdate() {
        // 如果用户正在交互，延迟更新
        if (this.userInteractionState.isInteracting) {
            console.log('[DeviceRenderer] 用户正在交互，延迟更新');
            return false;
        }

        // 如果有打开的下拉菜单，延迟更新
        if (this.userInteractionState.openDropdowns.size > 0) {
            console.log('[DeviceRenderer] 有打开的下拉菜单，延迟更新');
            return false;
        }

        // 如果正在渲染中，延迟更新
        if (this.renderingState.isRendering) {
            console.log('[DeviceRenderer] 正在渲染中，延迟更新');
            return false;
        }

        return true;
    }

    /**
     * 渲染设备列表
     * @param {Array} devices 设备列表
     * @param {Object} options 渲染选项
     */
    renderDeviceTable(devices = [], options = {}) {
        console.log('[DEBUG] renderDeviceTable 开始渲染，设备数:', devices.length);
        console.log('[DEBUG] 设备数据样本:', devices.slice(0, 2));

        // 检查是否可以更新
        if (!this.canUpdate()) {
            // 如果不能立即更新，保存待更新的数据
            this.renderingState.pendingUpdate = { devices, options };
            console.log('[DeviceRenderer] 更新被延迟，保存待更新数据');

            // 设置延迟更新
            setTimeout(() => {
                if (this.renderingState.pendingUpdate && this.canUpdate()) {
                    const { devices: pendingDevices, options: pendingOptions } = this.renderingState.pendingUpdate;
                    this.renderingState.pendingUpdate = null;
                    this.renderDeviceTable(pendingDevices, pendingOptions);
                }
            }, 1000);
            return;
        }

        // 计算数据哈希，检查是否真的需要更新
        const dataHash = this.calculateDataHash(devices);
        if (dataHash === this.renderingState.lastRenderHash && !options.forceUpdate) {
            console.log('[DeviceRenderer] 数据未变化，跳过渲染');
            return;
        }

        this.renderingState.isRendering = true;
        this.renderingState.lastRenderHash = dataHash;
        this.currentDevices = devices;

        if (!this.elements.deviceTableBody) {
            console.error('[DeviceRenderer] 设备表格容器未找到');
            console.log('[DEBUG] DOM元素状态:', {
                deviceTableBody: !!this.elements.deviceTableBody,
                deviceTable: !!this.elements.deviceTable,
                emptyState: !!this.elements.emptyState
            });
            this.renderingState.isRendering = false;
            return;
        }

        console.log('[DEBUG] 设备表格容器找到，开始渲染');

        try {
            // 保存当前打开的下拉菜单状态
            const openDropdowns = new Set(this.userInteractionState.openDropdowns);

            // 重置表格容器高度
            this.resetTableContainerHeight();

            // 清空现有内容
            this.elements.deviceTableBody.innerHTML = '';

            // 检查是否有数据
            if (devices.length === 0) {
                console.log('[DEBUG] 设备数据为空，显示空状态');
                this.showEmptyState();
                return;
            }

            console.log('[DEBUG] 设备数据不为空，隐藏空状态，开始渲染设备行');

            // 隐藏空状态
            this.hideEmptyState();

            // 渲染设备行
            console.log('[DEBUG] 开始渲染设备行，总数:', devices.length);
            const fragment = document.createDocumentFragment();
            devices.forEach((device, index) => {
                try {
                    console.log(`[DEBUG] 渲染设备 ${index + 1}/${devices.length}:`, device.imsi);
                    const row = this.createDeviceRow(device, index);
                    fragment.appendChild(row);
                    console.log(`[DEBUG] 设备行 ${device.imsi} 创建成功`);
                } catch (error) {
                    console.error(`[DeviceRenderer] 渲染设备行失败 (${device.imsi}):`, error);
                    console.error('[DEBUG] 错误详情:', error.stack);
                }
            });

            console.log('[DEBUG] 所有设备行创建完成，添加到DOM');
            this.elements.deviceTableBody.appendChild(fragment);
            console.log('[DEBUG] 设备行已添加到DOM');

            // 恢复之前打开的下拉菜单状态
            setTimeout(() => {
                this.restoreDropdownStates(openDropdowns);
                this.resetTableContainerHeight();
            }, 0);

            // 发送渲染完成事件
            emit(EVENT_TYPES.DEVICE_LIST_RENDERED, {
                count: devices.length,
                timestamp: Date.now()
            });

            console.log(`[DeviceRenderer] 已渲染 ${devices.length} 个设备`);

        } finally {
            this.renderingState.isRendering = false;
        }
    }

    /**
     * 创建设备行元素
     * @param {Object} device 设备数据
     * @param {number} index 索引
     * @returns {HTMLElement} 设备行元素
     * @private
     */
    createDeviceRow(device, index) {
        console.log(`[DEBUG] createDeviceRow 开始创建设备行:`, device.imsi);
        console.log(`[DEBUG] 设备数据:`, device);

        const row = document.createElement('tr');
        row.className = 'hover:bg-base-200 cursor-pointer';
        row.dataset.imsi = device.imsi;
        
        // 添加点击事件
        row.addEventListener('click', (e) => {
            // 如果点击的是操作按钮区域，不触发行选择事件
            if (e.target.closest('.dropdown')) {
                return;
            }
            emit(EVENT_TYPES.DEVICE_SELECTED, device);
        });

        row.innerHTML = `
            <td class="px-4 py-3">
                <div class="font-semibold text-sm text-base-content">
                    ${this.escapeHtml(device.imsi)}
                </div>
            </td>
            <td class="px-4 py-3">
                <span class="status-badge ${device.current_status === 1 ? 'online' : 'offline'}">
                    <span class="w-2 h-2 rounded-full ${device.current_status === 1 ? 'bg-green-500' : 'bg-red-500'}"></span>
                    ${StatusUtils.getStatusText(device.current_status)}
                </span>
            </td>
            <td class="px-4 py-3">
                ${this.renderHeartbeatTime(device.last_heartbeat_time)}
            </td>
            <td class="px-4 py-3">
                <div class="text-sm">
                    ${this.escapeHtml(device.position.location_name)}
                </div>
                <div class="text-xs text-base-content/50">
                    ${device.speed >= 0 ? device.speed + ' km/h' : '未知'}
                </div>
            </td>
            <td class="px-4 py-3">
                <div class="flex items-center justify-center">
                    <div class="radial-progress text-primary bg-base-300" style="--value:${device.battery_level || 0}; --size:2.5rem; --thickness: 3px;" role="progressbar">
                        <span class="text-xs font-medium">${device.battery_level || 0}%</span>
                    </div>
                </div>
            </td>
            <td class="px-4 py-3">
                <span class="badge badge-outline badge-sm">
                    ${StatusUtils.getWorkModeText(device.work_mode)}
                </span>
            </td>
            <td class="px-4 py-3">
                <div class="flex items-center gap-2">
                    <div class="rating rating-sm">
                        ${this.renderSignalStrength(device.signal_strength)}
                    </div>
                    <span class="text-xs text-base-content/50">${device.signal_strength}/5</span>
                </div>
            </td>
            <td class="px-4 py-3">
                <div class="dropdown dropdown-end">
                    <div tabindex="0" role="button" class="btn btn-ghost btn-sm btn-circle">
                        <i class="fas fa-ellipsis-v"></i>
                    </div>
                    <ul tabindex="0" class="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52">
                        ${device.imsi ? `<li><a class="device-action" data-action="detail" data-imsi="${this.escapeHtml(device.imsi)}">
                            <i class="fas fa-info-circle"></i> 查看详情
                        </a></li>` : ''}
                        ${device.imsi ? `<li><a class="device-action" data-action="history" data-imsi="${this.escapeHtml(device.imsi)}">
                            <i class="fas fa-history"></i> 历史记录
                        </a></li>` : ''}
                        ${device.imsi ? `<li><a class="device-action" data-action="export" data-imsi="${this.escapeHtml(device.imsi)}">
                            <i class="fas fa-download"></i> 导出数据
                        </a></li>` : ''}
                        ${!device.imsi ? `<li><a class="text-gray-400 cursor-not-allowed">
                            <i class="fas fa-exclamation-triangle"></i> IMSI无效
                        </a></li>` : ''}
                    </ul>
                </div>
            </td>
        `;

        return row;
    }

    /**
     * 渲染心跳时间
     * @param {string} heartbeatTime 心跳时间
     * @returns {string} HTML字符串
     */
    renderHeartbeatTime(heartbeatTime) {
        // 检查是否为无效时间
        if (!heartbeatTime || heartbeatTime === '0001-01-01T00:00:00Z') {
            return `
                <div class="text-sm text-base-content/50">
                    <i class="fas fa-question-circle mr-1"></i>
                    未知
                </div>
                <div class="text-xs text-base-content/30">
                    暂无心跳数据
                </div>
            `;
        }

        const dateObj = new Date(heartbeatTime);
        if (isNaN(dateObj.getTime())) {
            return `
                <div class="text-sm text-base-content/50">
                    <i class="fas fa-exclamation-triangle mr-1"></i>
                    无效时间
                </div>
            `;
        }

        return `
            <div class="text-sm">
                ${DateUtils.format(heartbeatTime, 'datetime')}
            </div>
            <div class="text-xs text-base-content/50">
                ${DateUtils.format(heartbeatTime, 'relative')}
            </div>
        `;
    }

    /**
     * 渲染信号强度
     * @param {number} strength 信号强度 (1-5)
     * @returns {string} 信号强度HTML
     * @private
     */
    renderSignalStrength(strength) {
        let html = '';
        for (let i = 1; i <= 5; i++) {
            const filled = i <= strength;
            html += `<input type="radio" class="mask mask-star-2 bg-${filled ? 'warning' : 'base-300'}" disabled />`;
        }
        return html;
    }

    /**
     * 渲染分页组件
     * @param {number} total 总数量
     * @param {number} current 当前页
     * @param {number} pageSize 每页大小
     */
    renderPagination(total, current = 1, pageSize = this.pageSize) {
        this.totalCount = total;
        this.currentPage = current;
        this.pageSize = pageSize;

        if (!this.elements.pagination) {
            console.error('[DeviceRenderer] 分页容器未找到');
            return;
        }

        const totalPages = Math.ceil(total / pageSize);
        
        if (totalPages <= 1) {
            this.elements.pagination.innerHTML = '';
            return;
        }

        const pagination = this.createPaginationElement(totalPages, current);
        this.elements.pagination.innerHTML = '';
        this.elements.pagination.appendChild(pagination);
    }

    /**
     * 创建分页元素
     * @param {number} totalPages 总页数
     * @param {number} currentPage 当前页
     * @returns {HTMLElement} 分页元素
     * @private
     */
    createPaginationElement(totalPages, currentPage) {
        const container = document.createElement('div');
        container.className = 'join';

        // 上一页按钮
        const prevBtn = document.createElement('button');
        prevBtn.className = `join-item btn ${currentPage === 1 ? 'btn-disabled' : ''}`;
        prevBtn.innerHTML = '<i class="fas fa-chevron-left"></i>';
        prevBtn.disabled = currentPage === 1;
        prevBtn.addEventListener('click', () => {
            if (currentPage > 1) {
                this.changePage(currentPage - 1);
            }
        });
        container.appendChild(prevBtn);

        // 页码按钮
        const maxVisible = UI_CONFIG.PAGINATION.MAX_VISIBLE_PAGES;
        const startPage = Math.max(1, currentPage - Math.floor(maxVisible / 2));
        const endPage = Math.min(totalPages, startPage + maxVisible - 1);

        for (let i = startPage; i <= endPage; i++) {
            const pageBtn = document.createElement('button');
            pageBtn.className = `join-item btn ${i === currentPage ? 'btn-active' : ''}`;
            pageBtn.textContent = i;
            pageBtn.addEventListener('click', () => {
                this.changePage(i);
            });
            container.appendChild(pageBtn);
        }

        // 下一页按钮
        const nextBtn = document.createElement('button');
        nextBtn.className = `join-item btn ${currentPage === totalPages ? 'btn-disabled' : ''}`;
        nextBtn.innerHTML = '<i class="fas fa-chevron-right"></i>';
        nextBtn.disabled = currentPage === totalPages;
        nextBtn.addEventListener('click', () => {
            if (currentPage < totalPages) {
                this.changePage(currentPage + 1);
            }
        });
        container.appendChild(nextBtn);

        return container;
    }

    /**
     * 更新统计信息
     * @param {Object} stats 统计数据
     */
    updateStatistics(stats) {
        this.currentStats = stats;

        if (!this.elements.statistics) {
            console.error('[DeviceRenderer] 统计信息容器未找到');
            return;
        }

        // 保持原有的卡片布局，只更新数据值
        // 更新总设备数
        const totalDevicesValue = document.getElementById('totalDevicesValue');
        if (totalDevicesValue) {
            totalDevicesValue.textContent = stats.total_devices || 0;
            totalDevicesValue.classList.remove('hidden');
        }
        const totalDevicesLoading = document.getElementById('totalDevicesLoading');
        if (totalDevicesLoading) {
            totalDevicesLoading.classList.add('hidden');
        }

        // 更新在线设备数
        const onlineDevicesValue = document.getElementById('onlineDevicesValue');
        if (onlineDevicesValue) {
            onlineDevicesValue.textContent = stats.online_devices || 0;
            onlineDevicesValue.classList.remove('hidden');
        }
        const onlineDevicesLoading = document.getElementById('onlineDevicesLoading');
        if (onlineDevicesLoading) {
            onlineDevicesLoading.classList.add('hidden');
        }

        // 更新离线设备数
        const offlineDevicesValue = document.getElementById('offlineDevicesValue');
        if (offlineDevicesValue) {
            offlineDevicesValue.textContent = stats.offline_devices || 0;
            offlineDevicesValue.classList.remove('hidden');
        }
        const offlineDevicesLoading = document.getElementById('offlineDevicesLoading');
        if (offlineDevicesLoading) {
            offlineDevicesLoading.classList.add('hidden');
        }

        // 更新异常设备数
        const errorDevicesValue = document.getElementById('errorDevicesValue');
        if (errorDevicesValue) {
            errorDevicesValue.textContent = stats.error_devices || 0;
            errorDevicesValue.classList.remove('hidden');
        }
        const errorDevicesLoading = document.getElementById('errorDevicesLoading');
        if (errorDevicesLoading) {
            errorDevicesLoading.classList.add('hidden');
        }

        // 更新在线率和离线率
        const onlineRate = document.getElementById('onlineRate');
        if (onlineRate) {
            onlineRate.textContent = `${stats.online_rate || 0}%`;
        }
        const offlineRate = document.getElementById('offlineRate');
        if (offlineRate) {
            offlineRate.textContent = `${stats.offline_rate || 0}%`;
        }

        console.log('[DeviceRenderer] 统计信息已更新:', stats);
    }

    // ==================== 状态管理方法 ====================

    /**
     * 显示空状态
     */
    showEmptyState() {
        if (this.elements.emptyState) {
            this.elements.emptyState.classList.remove('hidden');
        }
        if (this.elements.deviceTable) {
            this.elements.deviceTable.classList.add('hidden');
        }
    }

    /**
     * 隐藏空状态
     */
    hideEmptyState() {
        if (this.elements.emptyState) {
            this.elements.emptyState.classList.add('hidden');
        }
        if (this.elements.deviceTable) {
            this.elements.deviceTable.classList.remove('hidden');
        }
    }

    /**
     * 显示加载状态
     */
    showLoading() {
        DOMUtils.showLoading(true);
    }

    /**
     * 隐藏加载状态
     */
    hideLoading() {
        DOMUtils.showLoading(false);
    }

    // ==================== 工具方法 ====================

    /**
     * 重置表格容器高度，确保自适应内容
     * @private
     */
    resetTableContainerHeight() {
        const tableContainer = document.querySelector('.table-container');
        if (tableContainer) {
            // 强制重置所有可能影响高度的样式
            tableContainer.style.height = 'auto';
            tableContainer.style.maxHeight = 'none';
            tableContainer.style.minHeight = 'auto';
            tableContainer.style.overflowY = 'visible';

            console.log('[DEBUG] 表格容器高度已重置为自适应');
        }

        // 同时重置表格本身的高度
        if (this.elements.deviceTable) {
            this.elements.deviceTable.style.height = 'auto';
            this.elements.deviceTable.style.maxHeight = 'none';
            this.elements.deviceTable.style.minHeight = 'auto';

            console.log('[DEBUG] 表格高度已重置为自适应');
        }
    }

    /**
     * HTML转义
     * @param {string} text 文本
     * @returns {string} 转义后的文本
     * @private
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * 切换页面
     * @param {number} page 页码
     * @private
     */
    changePage(page) {
        if (page !== this.currentPage) {
            emit(EVENT_TYPES.PAGE_CHANGED, {
                page,
                pageSize: this.pageSize,
                previousPage: this.currentPage
            });
        }
    }

    /**
     * 清空表格
     */
    clearTable() {
        if (this.elements.deviceTableBody) {
            this.elements.deviceTableBody.innerHTML = '';
        }
        this.currentDevices = [];
    }

    /**
     * 获取当前设备列表
     * @returns {Array} 当前设备列表
     */
    getCurrentDevices() {
        return [...this.currentDevices];
    }

    /**
     * 获取当前统计信息
     * @returns {Object} 当前统计信息
     */
    getCurrentStats() {
        return { ...this.currentStats };
    }

    /**
     * 计算数据哈希值，用于检测数据变化
     * @param {Array} devices 设备列表
     * @returns {string} 数据哈希值
     * @private
     */
    calculateDataHash(devices) {
        if (!devices || devices.length === 0) {
            return 'empty';
        }

        // 创建一个简化的数据结构用于哈希计算
        const hashData = devices.map(device => ({
            imsi: device.imsi,
            status: device.current_status,
            heartbeat: device.last_heartbeat_time,
            battery: device.battery_level,
            signal: device.signal_strength,
            mode: device.work_mode,
            location: `${device.latitude},${device.longitude}`,
            speed: device.speed
        }));

        // 简单的哈希算法
        const str = JSON.stringify(hashData);
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }
        return hash.toString();
    }

    /**
     * 恢复下拉菜单状态
     * @param {Set} openDropdowns 之前打开的下拉菜单IMSI集合
     * @private
     */
    restoreDropdownStates(openDropdowns) {
        if (openDropdowns.size === 0) return;

        console.log('[DeviceRenderer] 恢复下拉菜单状态:', Array.from(openDropdowns));

        openDropdowns.forEach(imsi => {
            const row = this.elements.deviceTableBody.querySelector(`tr[data-imsi="${imsi}"]`);
            if (row) {
                const dropdown = row.querySelector('.dropdown');
                const button = dropdown?.querySelector('[role="button"]');
                if (button) {
                    // 重新打开下拉菜单
                    button.focus();
                    this.userInteractionState.openDropdowns.add(imsi);
                    console.log(`[DeviceRenderer] 恢复下拉菜单: ${imsi}`);
                }
            }
        });
    }

    /**
     * 强制更新渲染（忽略交互状态检查）
     * @param {Array} devices 设备列表
     * @param {Object} options 渲染选项
     */
    forceUpdate(devices = [], options = {}) {
        console.log('[DeviceRenderer] 强制更新渲染');

        // 临时清除交互状态
        const wasInteracting = this.userInteractionState.isInteracting;
        const openDropdowns = new Set(this.userInteractionState.openDropdowns);

        this.userInteractionState.isInteracting = false;
        this.userInteractionState.openDropdowns.clear();

        // 执行渲染
        this.renderDeviceTable(devices, { ...options, forceUpdate: true });

        // 恢复交互状态
        this.userInteractionState.isInteracting = wasInteracting;
        this.userInteractionState.openDropdowns = openDropdowns;
    }

    /**
     * 更新现有行的数据（不重新渲染整个表格和分页）
     * @param {Array} devices 设备列表
     */
    updateExistingRows(devices = []) {
        console.log('[DEBUG] updateExistingRows 开始更新现有行，设备数:', devices.length);

        if (!this.elements.deviceTableBody) {
            console.error('[DeviceRenderer] 设备表格容器未找到，无法更新行');
            return;
        }

        // 获取当前表格中的所有行
        const existingRows = this.elements.deviceTableBody.querySelectorAll('tr[data-imsi]');
        console.log('[DEBUG] 当前表格行数:', existingRows.length);

        // 创建设备数据的映射，以IMSI为键
        const deviceMap = new Map();
        devices.forEach(device => {
            deviceMap.set(device.imsi, device);
        });

        // 更新现有行的数据
        existingRows.forEach(row => {
            const deviceId = row.getAttribute('data-imsi');
            const updatedDevice = deviceMap.get(deviceId);

            if (updatedDevice) {
                this.updateSingleRow(row, updatedDevice);
                console.log('[DEBUG] 更新设备行:', deviceId);
            }
        });

        // 更新当前设备列表引用
        this.currentDevices = devices;

        console.log('[DEBUG] updateExistingRows 完成');
    }

    /**
     * 更新单个设备行的数据
     * @param {HTMLElement} row 行元素
     * @param {Object} device 设备数据
     * @private
     */
    updateSingleRow(row, device) {
        // 更新状态
        const statusCell = row.cells[1]; // 第二列是状态列
        if (statusCell) {
            const statusBadge = statusCell.querySelector('.status-badge');
            if (statusBadge) {
                const statusText = StatusUtils.getStatusText(device.current_status);
                const statusClass = device.current_status === 1 ? 'online' : 'offline';
                statusBadge.innerHTML = `
                    <span class="w-2 h-2 rounded-full ${device.current_status === 1 ? 'bg-green-500' : 'bg-red-500'}"></span>
                    ${statusText}
                `;
                statusBadge.className = `status-badge ${statusClass}`;
            }
        }

        // 更新最后心跳时间
        const heartbeatCell = row.cells[2]; // 第三列是心跳时间列
        if (heartbeatCell && device.last_heartbeat_time) {
            heartbeatCell.innerHTML = this.renderHeartbeatTime(device.last_heartbeat_time);
        }

        // 更新位置信息
        const locationCell = row.cells[3]; // 第四列是位置信息列
        if (locationCell) {
            locationCell.innerHTML = `
                <div class="text-sm">
                    ${this.escapeHtml(device.position.location_name)}
                </div>
                <div class="text-xs text-base-content/50">
                    ${device.speed >= 0 ? device.speed + ' km/h' : '未知'}
                </div>
            `;
        }

        // 更新电池信息
        const batteryCell = row.cells[4]; // 第五列是电池信息列
        if (batteryCell && device.battery_level !== undefined) {
            const batteryLevel = Math.round(device.battery_level);
            batteryCell.innerHTML = `
                <div class="flex items-center justify-center">
                    <div class="radial-progress text-primary bg-base-300" style="--value:${batteryLevel}; --size:2.5rem; --thickness: 3px;" role="progressbar">
                        <span class="text-xs font-medium">${batteryLevel}%</span>
                    </div>
                </div>
            `;
        }

        // 更新工作模式
        const workModeCell = row.cells[5]; // 第六列是工作模式列
        if (workModeCell && device.work_mode !== undefined) {
            const modeText = StatusUtils.getWorkModeText(device.work_mode);
            workModeCell.innerHTML = `
                <span class="badge badge-outline badge-sm">
                    ${modeText}
                </span>
            `;
        }

        // 更新信号强度
        const signalCell = row.cells[6]; // 第七列是信号强度列
        if (signalCell && device.signal_strength !== undefined) {
            signalCell.innerHTML = `
                <div class="flex items-center gap-2">
                    <div class="rating rating-sm">
                        ${this.renderSignalStrength(device.signal_strength)}
                    </div>
                    <span class="text-xs text-base-content/50">${device.signal_strength}/5</span>
                </div>
            `;
        }
    }
}

// ==================== 全局设备渲染器实例 ====================
export const deviceRenderer = new DeviceRenderer();

// ==================== 便捷函数 ====================
/**
 * 渲染设备表格的便捷函数
 * @param {Array} devices 设备列表
 * @param {Object} options 渲染选项
 */
export const renderDeviceTable = (devices, options) =>
    deviceRenderer.renderDeviceTable(devices, options);

/**
 * 渲染分页的便捷函数
 * @param {number} total 总数量
 * @param {number} current 当前页
 * @param {number} pageSize 每页大小
 */
export const renderPagination = (total, current, pageSize) =>
    deviceRenderer.renderPagination(total, current, pageSize);

/**
 * 更新统计信息的便捷函数
 * @param {Object} stats 统计数据
 */
export const updateStatistics = (stats) =>
    deviceRenderer.updateStatistics(stats);

// ==================== 默认导出 ====================
export default deviceRenderer;
