root = "."
tmp_dir = "tmp"

[build]
# 只需要写你的项目入口文件
cmd = "go build -o ./tmp/main server.go"
# 由于你的入口文件是 server.go
bin = "./tmp/main"
# 要监听的文件后缀
include_ext = ["go", "tpl", "tmpl", "html", "toml"]
# 要排除的文件
exclude_dir = ["assets", "tmp", "vendor", "frontend/node_modules", "tcp-testbench/frontend/node_modules", "logs", "test_logs", "pix_log"]
# 是否包含子目录
include_dir = []
# 忽略这些文件
exclude_file = []
# 排除以下文件的修改
exclude_regex = ["_test.go"]
# 忽略文件名包含这些字符的文件
exclude_unchanged = true
# 监听文件变化的间隔时间
follow_symlink = true
# 日志文件
log = "air.log"
# 如果文件更改过于频繁，则没有必要在每次更改时都重新构建。
delay = 1000 # ms
# 发生构建错误时，停止运行旧的二进制文件。
stop_on_error = true
# 在终止进程之前发送中断信号 (windows 不支持此功能)
send_interrupt = false
# 发送中断信号后的延迟时间
kill_delay = 500 # ms
# 重新启动进程时添加额外的参数 (支持通配符)
args_bin = []

[log]
# 显示日志时间
time = false

[color]
# 自定义每个部分显示的颜色
main = "magenta"
watcher = "cyan"
build = "yellow"
runner = "green"

[misc]
# 退出时是否删除tmp目录
clean_on_exit = true