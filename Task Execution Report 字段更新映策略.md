

# Task Execution Report 字段更新映射策略

## 概述

本文档详细描述了不同心跳step值与task_execution_report表字段更新的对应关系，包括更新逻辑、数据来源、防护机制和最新的"只增不减"策略。

## 🆕 最新更新

- **🆕 到达时间记录**：Step=2时，新增`arrival_time`字段记录实际到达时间
- **计划值"只增不减"策略**：Step=1时，`planned_duration_min`和`planned_distance_m`字段实现只增不减的更新机制
- **实际速度计算**：在任务结束时自动计算并更新`actual_speed_kmh`字段
- **实际执行时长优化**：使用服务器记录的`departure_time`计算，提高准确性
- **全面重复更新防护**：所有step值都实现了重复更新防护机制

## 主要映射表

| Step值 | 任务状态描述 | 对应Status | 调用函数 | 重复更新防护 |
|--------|-------------|-----------|----------|-------------|
| 0 | 未开始/无任务 | - | `updateExecutionReportOnNoTask` | ❌ |
| 1 | 正在执行 | 2 | `updateExecutionReportOnStart` | ✅ |
| 2 | 已完成 | 3 | `updateExecutionReportOnComplete` | ✅ |
| 3 | 已取消 | 5 | `updateExecutionReportOnCancel` | ✅ |
| 6 | 已暂停 | 4 | `updateExecutionReportOnPause` | ✅ |
| 8 | 已终止 | 6 | `updateExecutionReportOnTerminate` | ✅ |
| 9 | 无法完成 | 7 | `updateExecutionReportOnFail` | ✅ |

## Step = 0 (TaskID = 0, 无任务)

| 属性 | 值 |
|------|-----|
| **调用函数** | `updateExecutionReportOnNoTask` |
| **重复更新防护** | ❌ 无防护机制 |
| **更新条件** | `imsi = ? AND status IN (1, 2, 4)` (将要执行、正在执行、已暂停) |

**更新字段**:

| 字段名 | 更新值 | 更新逻辑 | 数据来源 | 原始协议字段 |
|--------|--------|----------|----------|-------------|
| status | 6 (已终止) | 终止所有执行中的实例 | 固定值 | - |
| updated_time | 当前时间戳 | `time.Now().Unix()` | 服务器时间 | - |
| end_time | 当前时间 | `time.Now()` | 服务器时间 | - |
| termination_reason | 终止原因描述 | 格式化字符串 | 系统生成 | - |

## Step = 1 (任务开始)

| 属性 | 值 |
|------|-----|
| **调用函数** | `updateExecutionReportOnStart` |
| **重复更新防护** | ✅ 检查当前状态是否已为2(正在执行)，如是则跳过更新 |
| **🆕 特殊策略** | 计划值"只增不减"策略：`planned_duration_min`和`planned_distance_m`只有在新值大于当前值时才更新 |

**更新字段**:

| 字段名                  | 更新值      | 更新逻辑                                               | 数据来源       | 原始协议字段                           | 🆕 特殊策略 |
| -------------------- | -------- | -------------------------------------------------- | ---------- | -------------------------------- | ------- |
| departure_time       | 当前时间     | `time.Now()`                                       | 服务器时间      | -                                | -       |
| status               | 2 (正在执行) | 固定值                                                | 状态映射       | -                                | -       |
| updated_time         | 当前时间戳    | `time.Now().Unix()`                                | 服务器时间      | -                                | -       |
| planned_duration_min | 计划时长     | 只增不减策略：新值 > 当前值时才更新                                | 心跳数据       | `latestTask.leftTime`            | ✅ 只增不减  |
| planned_distance_m   | 计划距离     | 只增不减策略：新值 > 当前值时才更新                                | 心跳数据       | `latestTask.taskDistance`        | ✅ 只增不减  |
| start_location       | 起始位置     | `t.heartbeatDevice.LatestTask.CurrentStation.Name` | 心跳数据       | `latestTask.currentStation.name` | -       |
| end_location         | 目标位置     | `t.heartbeatDevice.LatestTask.TargetStation.Name`  | 心跳数据       | `latestTask.targetStation.name`  | -       |
| start_mileage_m      | 开始里程     | `int(t.heartbeatDevice.TotalMileage * 1000)`       | 心跳数据(km→m) | `tm`                             | -       |
| 🆕 start_cm          | 开始CM值     | `int(t.heartbeatDevice.CM)`                        | 心跳数据       | `cm`                             | ✅       |

### 🆕 计划值"只增不减"策略详解

**策略原理**：
- **首次记录**：当数据库中的值为0时，直接更新
- **只增不减**：后续更新只有当新值大于当前数据库值时才执行
- **保持稳定**：新值小于等于当前值时，保持数据库原值不变

**实现逻辑**：
```go
// 查询当前数据库中的计划值
var currentValues struct {
    PlannedDurationMin int `json:"planned_duration_min"`
    PlannedDistanceM   int `json:"planned_distance_m"`
}

// planned_duration_min: 只有当新值大于当前值时才更新
if t.heartbeatDevice.LatestTask.LeftTime > 0 {
    if currentPlannedDuration == 0 || t.heartbeatDevice.LatestTask.LeftTime > currentPlannedDuration {
        updates["planned_duration_min"] = t.heartbeatDevice.LatestTask.LeftTime
        // 记录更新日志
    } else {
        // 记录保持不变的日志
    }
}
```

**使用场景示例**：
1. **第一次 step=1**：`LeftTime=2, TaskDistance=1515` → 直接更新
2. **第二次 step=1**：`LeftTime=1, TaskDistance=1513` → 保持不变（新值≤当前值）
3. **第三次 step=1**：`LeftTime=3, TaskDistance=1514` → 只更新duration（3>2），distance保持不变（1514≤1515）

## Step = 2 (任务完成)

| 属性 | 值 |
|------|-----|
| **调用函数** | `updateExecutionReportOnComplete` |
| **重复更新防护** | ✅ 检查当前状态是否已为3(已完成)，如是则跳过更新 |

**更新字段**:

| 字段名                 | 更新值     | 更新逻辑                                                 | 数据来源       | 原始协议字段 | 🆕 新增 |
| ------------------- | ------- | ---------------------------------------------------- | ---------- | ------ | ------- |
| end_time            | 当前时间    | `time.Now()`                                         | 服务器时间      | -      | -       |
| 🆕 arrival_time     | 当前时间    | `time.Now()`                                         | 服务器时间      | -      | ✅       |
| status              | 3 (已完成) | `mapStepToStatus(2)`                                 | 状态映射       | -      | -       |
| updated_time        | 当前时间戳   | `time.Now().Unix()`                                  | 服务器时间      | -      | -       |
| actual_duration_min | 实际时长    | `int64(now.Sub(departureTime).Seconds()) / 60`       | 计算值        | -      | -       |
| end_mileage_m       | 结束里程    | `int(t.heartbeatDevice.TotalMileage * 1000)`         | 心跳数据(km→m) | `tm`   | -       |
| 🆕 end_cm           | 结束CM值    | `int(t.heartbeatDevice.CM)`                          | 心跳数据      | `cm`   | ✅       |
| actual_mileage_m    | 实际里程    | `endMileage - startMileage`                          | 计算值        | -      | -       |
| actual_speed_kmh    | 实际速度    | `(actual_mileage_m/1000) / (actual_duration_min/60)` | 计算值        | -      | ✅       |

### 🆕 到达时间字段说明

**`arrival_time`字段用途**：
- **记录目的**：准确记录车辆实际到达目标位置的时间
- **数据来源**：服务器时间，确保时间的一致性和准确性
- **业务价值**：用于计算准点率、分析运行效率、生成运营报告
- **与end_time的区别**：`arrival_time`专门记录到达时间，`end_time`记录任务结束时间，两者在业务语义上有所区分

## Step = 3 (任务取消)

| 属性 | 值 |
|------|-----|
| **调用函数** | `updateExecutionReportOnCancel` |
| **重复更新防护** | ✅ 检查当前状态是否已为5(已取消)，如是则跳过更新 |

**更新字段**:

| 字段名 | 更新值 | 更新逻辑 | 数据来源 | 原始协议字段 |
|--------|--------|----------|----------|-------------|
| status | 5 (已取消) | `mapStepToStatus(3)` | 状态映射 | - |
| end_time | 当前时间 | `time.Now()` | 服务器时间 | - |
| updated_time | 当前时间戳 | `time.Now().Unix()` | 服务器时间 | - |
| termination_reason | 取消原因 | 格式化字符串包含TaskID、时间、IP | 系统生成 | - |
| actual_duration_min | 实际时长 | `int64(now.Sub(departureTime).Seconds()) / 60` | 计算值 | - |
| end_mileage_m | 结束里程 | `int(t.heartbeatDevice.TotalMileage * 1000)` | 心跳数据(km→m) | `tm` |
| 🆕 end_cm | 结束CM值 | `int(t.heartbeatDevice.CM)` | 心跳数据 | `cm` |
| actual_mileage_m | 实际里程 | `endMileage - startMileage` | 计算值 | - |
| actual_speed_kmh | 实际速度 | `(actual_mileage_m/1000) / (actual_duration_min/60)` | 计算值 | - |

## Step = 6 (任务暂停)

| 属性 | 值 |
|------|-----|
| **调用函数** | `updateExecutionReportOnPause` |
| **重复更新防护** | ✅ 检查当前状态是否已为4(已暂停)，如是则跳过更新 |
| **注意** | 暂停状态不更新end_time和里程相关字段 |

**更新字段**:

| 字段名 | 更新值 | 更新逻辑 | 数据来源 | 原始协议字段 |
|--------|--------|----------|----------|-------------|
| status | 4 (已暂停) | `mapStepToStatus(6)` | 状态映射 | - |
| updated_time | 当前时间戳 | `time.Now().Unix()` | 服务器时间 | - |

## Step = 8 (任务终止)

| 属性 | 值 |
|------|-----|
| **调用函数** | `updateExecutionReportOnTerminate` |
| **重复更新防护** | ✅ 检查当前状态是否已为6(已终止)，如是则跳过更新 |

**更新字段**:

| 字段名 | 更新值 | 更新逻辑 | 数据来源 | 原始协议字段 |
|--------|--------|----------|----------|-------------|
| status | 6 (已终止) | `mapStepToStatus(8)` | 状态映射 | - |
| end_time | 当前时间 | `time.Now()` | 服务器时间 | - |
| updated_time | 当前时间戳 | `time.Now().Unix()` | 服务器时间 | - |
| termination_reason | 终止原因 | 格式化字符串包含TaskID、时间、IP | 系统生成 | - |
| actual_duration_min | 实际时长 | `int64(now.Sub(departureTime).Seconds()) / 60` | 计算值 | - |
| end_mileage_m | 结束里程 | `int(t.heartbeatDevice.TotalMileage * 1000)` | 心跳数据(km→m) | `tm` |
| 🆕 end_cm | 结束CM值 | `int(t.heartbeatDevice.CM)` | 心跳数据 | `cm` |
| actual_mileage_m | 实际里程 | `endMileage - startMileage` | 计算值 | - |
| actual_speed_kmh | 实际速度 | `(actual_mileage_m/1000) / (actual_duration_min/60)` | 计算值 | - |

## Step = 9 (任务失败)

| 属性 | 值 |
|------|-----|
| **调用函数** | `updateExecutionReportOnFail` |
| **重复更新防护** | ✅ 检查当前状态是否已为7(无法完成)，如是则跳过更新 |

**更新字段**:

| 字段名 | 更新值 | 更新逻辑 | 数据来源 | 原始协议字段 |
|--------|--------|----------|----------|-------------|
| status | 7 (无法完成) | `mapStepToStatus(9)` | 状态映射 | - |
| end_time | 当前时间 | `time.Now()` | 服务器时间 | - |
| updated_time | 当前时间戳 | `time.Now().Unix()` | 服务器时间 | - |
| termination_reason | 失败原因 | 格式化字符串包含TaskID、时间、IP | 系统生成 | - |
| actual_duration_min | 实际时长 | `int64(now.Sub(departureTime).Seconds()) / 60` | 计算值 | - |
| end_mileage_m | 结束里程 | `int(t.heartbeatDevice.TotalMileage * 1000)` | 心跳数据(km→m) | `tm` |
| 🆕 end_cm | 结束CM值 | `int(t.heartbeatDevice.CM)` | 心跳数据 | `cm` |
| actual_mileage_m | 实际里程 | `endMileage - startMileage` | 计算值 | - |
| actual_speed_kmh | 实际速度 | `(actual_mileage_m/1000) / (actual_duration_min/60)` | 计算值 | - |

## 重复更新防护机制

### 防护实现方式

| 步骤 | 实现方式 |
|------|----------|
| 1. 查询当前状态 | `db.GetPixmoving().Table("task_execution_report").Where("id = ?", executionId).Select("status").Scan(&currentStatus)` |
| 2. 状态映射 | `expectedStatus := t.mapStepToStatus(stepValue)` |
| 3. 状态比较 | `if currentStatus == expectedStatus { return }` |

### 状态映射函数

| Step值 | 映射Status | 状态描述 |
|--------|-----------|----------|
| 1 | 2 | 正在执行 |
| 2 | 3 | 已完成 |
| 3 | 5 | 已取消 |
| 6 | 4 | 已暂停 |
| 8 | 6 | 已终止 |
| 9 | 7 | 无法完成 |
| 其他 | 1 | 将要执行 |

## 🆕 核心改进策略

### 1. 计划值"只增不减"策略 (Step=1专用)

| 特性 | 说明 |
|------|------|
| **适用字段** | `planned_duration_min`, `planned_distance_m` |
| **适用场景** | 仅在step=1时生效 |
| **更新条件** | 新值 > 当前数据库值 或 当前值为0（首次记录） |
| **保持条件** | 新值 ≤ 当前数据库值 |
| **设计目的** | 防止计划值因临时数据波动而异常降低，保持计划的稳定性 |

### 2. 实际速度自动计算 (Step=2/3/8/9)

| 特性 | 说明 |
|------|------|
| **计算公式** | `(actual_mileage_m/1000) / (actual_duration_min/60)` |
| **计算条件** | `actual_mileage_m > 0` 且 `actual_duration_min > 0` |
| **精度处理** | 四舍五入保留2位小数 |
| **错误处理** | 避免除零错误，条件不满足时不计算 |

### 3. 实际执行时长优化

| 改进点 | 原有方式 | 🆕 新方式 | 优势 |
|--------|----------|----------|------|
| **数据来源** | 设备提供的`StartTime` | 服务器记录的`departure_time` | 时间一致性更好 |
| **计算方式** | `now - StartTime` | `end_time - departure_time` | 避免设备时间不准确 |
| **依赖性** | 依赖设备时间戳 | 完全基于服务器时间 | 减少外部依赖 |

## 计算逻辑详解

### 1. 🆕 实际执行时长计算（优化后）

| 步骤 | 实现方式 | 说明 |
|------|----------|------|
| 获取开始时间 | `db.GetPixmoving().Table("task_execution_report").Where("id = ?", executionId).Select("departure_time").Scan(&departureTime)` | 从数据库获取服务器记录的开始时间 |
| 计算时长差 | `actualDurationSec := int64(now.Sub(departureTime).Seconds())` | 当前时间减去开始时间 |
| 转换单位 | `actualDurationMin := int(actualDurationSec / 60)` | 秒转换为分钟 |

### 2. 实际里程计算

| 步骤 | 实现方式 | 说明 |
|------|----------|------|
| 获取结束里程 | `endMileage := int(t.heartbeatDevice.TotalMileage * 1000)` | 心跳数据km转m |
| 获取开始里程 | `db.GetPixmoving().Table("task_execution_report").Where("id = ?", executionId).Select("start_mileage_m").Scan(&startMileage)` | 从数据库获取 |
| 计算实际里程 | `actualMileage := endMileage - startMileage` | 结束里程减去开始里程 |

### 3. 🆕 实际速度计算（新增）

| 步骤 | 实现方式 | 说明 |
|------|----------|------|
| 检查计算条件 | `if actualMileage > 0 && actualDurationMin > 0` | 避免除零错误 |
| 计算速度 | `actualSpeedKmh := float64(actualMileage) / 1000.0 / (float64(actualDurationMin) / 60.0)` | (里程米转公里) / (时间分钟转小时) |
| 保留精度 | `updates["actual_speed_kmh"] = float64(int(actualSpeedKmh*100+0.5)) / 100.0` | 四舍五入保留2位小数 |

## 字段更新汇总表

| Step值 | departure_time | 🆕 arrival_time | end_time | status | actual_duration_min | actual_mileage_m | 🆕 actual_speed_kmh | 🆕 planned_duration_min | 🆕 planned_distance_m | start_location | end_location | start_mileage_m | termination_reason |
|--------|---------------|----------------|----------|--------|-------------------|------------------|------------------|-------------------|-------------------|----------------|--------------|----------------|-------------------|
| 0 | - | - | ✅ | ✅ | - | - | - | - | - | - | - | - | ✅ |
| 1 | ✅ | - | - | ✅ | - | - | - | ✅ 只增不减 | ✅ 只增不减 | ✅ | ✅ | ✅ | - |
| 2 | - | ✅ 新增 | ✅ | ✅ | ✅ 优化算法 | ✅ | ✅ 自动计算 | - | - | - | - | - | - |
| 3 | - | - | ✅ | ✅ | ✅ 优化算法 | ✅ | ✅ 自动计算 | - | - | - | - | - | ✅ |
| 6 | - | - | - | ✅ | - | - | - | - | - | - | - | - | - |
| 8 | - | - | ✅ | ✅ | ✅ 优化算法 | ✅ | ✅ 自动计算 | - | - | - | - | - | ✅ |
| 9 | - | - | ✅ | ✅ | ✅ 优化算法 | ✅ | ✅ 自动计算 | - | - | - | - | - | ✅ |

### 图例说明

| 标识 | 含义 |
|------|------|
| ✅ | 正常更新 |
| ✅ 新增 | 新增字段的更新功能 |
| ✅ 只增不减 | 实现"只增不减"策略的字段 |
| ✅ 优化算法 | 使用优化后的计算算法 |
| ✅ 自动计算 | 新增的自动计算功能 |
| - | 不更新此字段 |

## 原始协议字段说明

| 原始协议字段 | 字段类型 | 单位 | 说明 | 对应Go结构体字段 |
|-------------|----------|------|------|------------------|
| `tm` | int32 | km | 总里程 | `TotalMileage` |
| `latestTask.leftTime` | int | 分钟 | 剩余时间/计划时长 | `LatestTask.LeftTime` |
| `latestTask.taskDistance` | int | 米 | 任务距离 | `LatestTask.TaskDistance` |
| `latestTask.currentStation.name` | string | - | 当前站点名称 | `LatestTask.CurrentStation.Name` |
| `latestTask.targetStation.name` | string | - | 目标站点名称 | `LatestTask.TargetStation.Name` |
| `latestTask.step` | int8 | - | 任务步骤状态 | `LatestTask.Step` |
| `latestTask.taskId` | int64 | - | 执行实例ID | `LatestTask.TaskID` |

## 注意事项

| 类别 | 说明 |
|------|------|
| **TaskID=0处理** | 当心跳中TaskID=0时，只调用`updateExecutionReportOnNoTask`终止现有实例 |
| **数据来源** | 时间字段使用服务器时间，里程字段来自心跳数据 |
| **单位转换** | 心跳里程数据从km转换为m存储 |
| **防护机制** | 除step=0外，所有状态更新都有重复防护 |
| **🆕 计划值策略** | Step=1时，`planned_duration_min`和`planned_distance_m`实现"只增不减"策略 |
| **🆕 速度计算** | Step=2/3/8/9时自动计算实际运行速度，保留2位小数 |
| **🆕 时长计算优化** | 使用服务器记录的`departure_time`计算实际执行时长，提高准确性 |
| **精度处理** | 速度保留2位小数，时长以分钟为单位 |
| **协议字段映射** | `tm`字段对应`TotalMileage`，单位从km转换为m存储 |

## 🆕 版本更新记录

| 版本 | 更新内容 | 影响范围 |
|------|----------|----------|
| **v2.2** | 🆕 新增到达时间记录功能 | Step=2的`arrival_time`字段 |
| **v2.1** | 实现计划值"只增不减"策略 | Step=1的`planned_duration_min`和`planned_distance_m`字段 |
| **v2.0** | 添加实际速度自动计算 | Step=2/3/8/9的`actual_speed_kmh`字段 |
| **v1.9** | 优化实际执行时长计算方式 | Step=2/3/8/9的`actual_duration_min`字段 |
| **v1.8** | 实现全面重复更新防护 | 所有step值的状态更新 |

## 🔍 故障排查指南

### 1. 计划值未更新
- **检查点1**：确认step=1且心跳数据中的值>0
- **检查点2**：确认新值大于数据库中的当前值
- **日志关键词**：`planned_duration_min保持不变` 或 `planned_distance_m保持不变`

### 2. 实际速度为0或异常
- **检查点1**：确认`actual_mileage_m > 0`且`actual_duration_min > 0`
- **检查点2**：检查开始和结束里程数据的准确性
- **检查点3**：确认任务执行时长计算正确

### 3. 重复更新问题
- **检查点1**：确认重复更新防护机制是否正常工作
- **日志关键词**：`跳过重复更新`
- **解决方案**：检查状态映射函数`mapStepToStatus`是否正确
