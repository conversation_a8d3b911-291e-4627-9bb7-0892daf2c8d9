package boot

import (
	"bytes"
	"ccserver/app/api"
	"ccserver/app/library/common"
	"ccserver/app/library/v"
	"ccserver/app/module/broadcast"
	"ccserver/app/module/client"
	"ccserver/app/module/dashboard"
	"ccserver/app/module/db"
	"ccserver/app/module/license"
	"ccserver/app/module/mqtt"
	"ccserver/app/module/upgrade"
	"ccserver/app/security"
	cacheService "ccserver/app/service/cache"
	dbService "ccserver/app/service/db"
	"ccserver/app/system"
	"ccserver/app/vars"
	"ccserver/app/websocket"
	"ccserver/pix_log"
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"log"
	"net"
	"net/http"
	"strings"
	"time"

	"github.com/gogf/gf/frame/g"

	"ccserver/app/module/analysis"

	"github.com/gogf/gf/encoding/gjson"
	"github.com/gogf/gf/net/gtcp"
)

// loadEnvironment 初始化环境
// func loadEnvironment() {
//	cacheService.EmptyRedisDeviceOfflineTs()
//	dbService.RecordAllDevicesOffline(true) // 将所有的终端状态变更为离线
//
//	cacheService.DelDeviceUnauthorized() // 删除未认证的终端 KEY
//	cacheService.DelOwnDevices(0)
//	cacheService.AddOwnDevicesAndInitHeartbeatTs(0)
//	_ = cacheService.ResetRedisIPPool() // 重置 IP 缓存池
//
//	upgrade.Start() // 升级业务
//	db.InitDbCrontabTask()
// }

func loadEnvironment() {
	startEnv := time.Now()
	fmt.Println("\n开始初始化环境...")

	t1 := time.Now()
	cacheService.EmptyRedisDeviceOfflineTs()
	fmt.Printf("清空Redis设备离线时间戳耗时: %v\n", time.Since(t1))

	t2 := time.Now()
	dbService.RecordAllDevicesOffline(true) // 将所有的终端状态变更为离线
	fmt.Printf("记录所有设备离线状态耗时: %v\n", time.Since(t2))

	t3 := time.Now()
	cacheService.DelDeviceUnauthorized() // 删除未认证的终端 KEY
	fmt.Printf("删除未认证终端耗时: %v\n", time.Since(t3))

	t4 := time.Now()
	cacheService.DelOwnDevices(0)
	fmt.Printf("删除自有设备耗时: %v\n", time.Since(t4))

	t5 := time.Now()
	cacheService.AddOwnDevicesAndInitHeartbeatTs(0)
	fmt.Printf("添加自有设备并初始化心跳时间戳耗时: %v\n", time.Since(t5))

	t6 := time.Now()
	_ = cacheService.ResetRedisIPPool() // 重置 IP 缓存池
	fmt.Printf("重置IP缓存池耗时: %v\n", time.Since(t6))

	t7 := time.Now()
	upgrade.Start() // 升级业务
	fmt.Printf("启动升级业务耗时: %v\n", time.Since(t7))

	t8 := time.Now()
	db.InitDbCrontabTask()
	fmt.Printf("初始化数据库定时任务耗时: %v\n", time.Since(t8))

	t9 := time.Now()

	fmt.Printf("启动心跳检测器耗时: %v\n", time.Since(t9))

	fmt.Printf("环境初始化总耗时: %v\n\n", time.Since(startEnv))
}

var flags = 1

// handleConnection TCP连接处理函数
func handleConnection(conn *gtcp.Conn, keepaliveTimeout time.Duration) {
	// 设置连接超时时间
	if e2 := conn.SetDeadline(time.Now().Add(keepaliveTimeout)); e2 != nil {
		log.Fatalf("[CONNECT]初始化有效时间失败: %s", e2)
	}

	// 初始化终端
	term := &vars.Terminal{}
	term.Conn = conn
	term.IsConnected = true
	remoteAddr := strings.Split(conn.RemoteAddr().String(), ":")
	ipAddr := net.ParseIP(remoteAddr[0]).String()
	term.Prop.IP = ipAddr
	term.Prop.Authorized = false
	term.Prop.Quit = make(chan bool, 1)
	term.Prop.Message = make(chan []byte, 20)
	term.Prop.ConnectAt = time.Now()
	term.Prop.UpdatedAt = time.Now()
	term.Prop.KeepaliveTimeout = keepaliveTimeout
	term.Parser = new(YhlProtocol)

	// 🆕 增强日志：记录新连接建立
	pix_log.Info("🔗 [NEW_CONNECTION] 新连接建立：IP=%s, 连接地址=%s, 超时时间=%v",
		ipAddr, conn.RemoteAddr(), keepaliveTimeout)

	// 🆕 安全检查：预检查IP连接限制（在设备注册前）
	// 注意：此时还没有IMSI，所以使用临时标识符进行IP检查
	if allowed, reason := security.CheckConnectionAllowed("", ipAddr); !allowed {
		pix_log.Warning("🚫 [SECURITY] IP连接被拒绝：IP=%s, 原因=%s", ipAddr, reason)
		conn.Close()
		return
	}

	// 检查许可证密钥
	err, maxNum := common.CheckKey()
	if err != nil {
		pix_log.Error("证书已过期：%v", err)
		return
	}

	if flags > maxNum {
		pix_log.Error("超过最大连接数")
		return
	}

	// 启动接收和解析线程
	go doRecvAndParse(term)
	flags++
	// 处理发送和断开连接
	doSendAndHandleDisconnect(term)
}

// Start 启动
// func Start() (err error) {
//	// 检查 MySQL 数据库连接
//	if e := g.DB().PingMaster(); e != nil {
//		return fmt.Errorf("(MySQL) %s", e)
//	}
//	// 检查 Redis 连接
//	if e := v.Redis().Conn().Err(); e != nil {
//		return fmt.Errorf("(Redis) %s", e)
//	}
//	// 加载许可证
//	if e := license.Load(); e != nil {
//		return e
//	}
//	// 用于初始化mqtt客户端和连接到一个 MQTT 服务器
//	if e := mqtt.Start(); e != nil {
//		return e
//	}
//	// 初始化数据库配置选项
//	if e := dbService.InitOptions(); e != nil {
//		return e
//	}
//
//	// 创建多个数据库连接
//	dbService.NewConnection()         // 默认数据库连接
//	dbService.NewConnectionWurenche() // 其他数据库连接
//	dbService.NewConnectionJzy()      // jzy 数据库连接
//	dbService.NewConnectionCoffee()   // coffee 数据库连接
//
//	// 初始化环境
//	loadEnvironment()
//
//	var (
//		tcpHost, appName string
//		tcpPort          int
//		keepaliveTimeout time.Duration
//	)
//	// 设置应用启动时间
//	vars.App.Start = time.Now().Format("2006-01-02 15:04:05")
//
//	// 获取 TCP 主机地址
//	if tcpHost = vars.Config.Server.Tcp.Host; tcpHost == "" {
//		tcpHost = "0.0.0.0"
//	}
//	// 获取 TCP 端口
//	if tcpPort = vars.Config.Server.Tcp.Port; tcpPort == 0 {
//		tcpPort = 2020
//	}
//	// 获取 TCP 保活超时时间
//	if keepaliveTimeout = vars.Config.Server.Tcp.KeepaliveTimeout; keepaliveTimeout == 0 {
//		keepaliveTimeout = 150
//	}
//	// 获取应用名称
//	if appName = vars.Config.Setting.AppName; appName == "" {
//		appName = "TCP SERVER 210527"
//	}
//
//	// 设置保活超时时间
//	keepaliveTimeout *= time.Second
//	// 构建 TCP URI
//	tcpURI := fmt.Sprintf("%s:%d", tcpHost, tcpPort)
//	// 打印服务器启动信息
//	var servMsg = `
// ------------------------------------------------------------------------
// ------------------------------------------------------------------------
// -----------------     %s
// --------              WEB  Server: %s
// --------              TCP  Server: %s
// --------              Start Time : %s
// --------                  Version: %s
// ------------------------------------------------------------------------
// ------------------------------------------------------------------------
// `
//	fmt.Printf(servMsg, appName, vars.Config.Server.Web.Address, tcpURI, vars.App.Start, vars.App.Ver)
//
//	// 启动 TCP 服务器
//	go func() {
//		if e := gtcp.NewServer(tcpURI, func(conn *gtcp.Conn) {
//			handleConnection(conn, keepaliveTimeout)
//		}, "ROUTER_TCP").Run(); e != nil {
//			log.Fatalf("[TCP]服务器启动失败: %s", e)
//		}
//	}()
//
//	// 启动 Kafka 消费者
//	go func() {
//		kafkaConsumer() // kafka消费者
//	}()
//
//	// 推送消息到 MQTT
//	dashboard.Push()
//
//	// 获取 MQTT 消息
//	go func() {
//		dashboard.GetMqttMsg()
//	}()
//
//	// go func() {
//	//	dashboard.Mqtt()
//	// }()
//
//	esClient, err := es.CreateClient()
//	if err != nil {
//		fmt.Println(err)
//	} else {
//		fmt.Println("连接成功: ", esClient)
//	}
//
//	// broadcast
//	go func() {
//		if e := broadcast.Run(); e != nil {
//			log.Fatalf("[BROADCAST]UDP服务器启动失败: %s", e)
//		}
//	}()
//
//	go doDbRelatedTask(true)
//
//	g.Wait()
//	return nil
// }

// Start 启动
func Start() (err error) {
	startTotal := time.Now()
	fmt.Println("开始启动服务...")

	// 检查 MySQL 数据库连接
	t1 := time.Now()
	fmt.Println("开始连接 MySQL...")
	db := g.DB()
	if e := db.PingMaster(); e != nil {
		return fmt.Errorf("(MySQL) %s", e)
	}
	fmt.Printf("MySQL连接耗时: %v\n", time.Since(t1))

	// 检查 Redis 连接
	t2 := time.Now()
	if e := v.Redis().Conn().Err(); e != nil {
		return fmt.Errorf("(Redis) %s", e)
	}
	fmt.Printf("Redis连接耗时: %v\n", time.Since(t2))

	// 加载许可证
	t3 := time.Now()
	if e := license.Load(); e != nil {
		return e
	}
	fmt.Printf("加载许可证耗时: %v\n", time.Since(t3))

	// 用于初始化mqtt客户端和连接到一个 MQTT 服务器
	t4 := time.Now()
	if e := mqtt.Start(); e != nil {
		return e
	}
	fmt.Printf("MQTT初始化耗时: %v\n", time.Since(t4))

	// 初始化数据库配置选项
	t5 := time.Now()
	if e := dbService.InitOptions(); e != nil {
		return e
	}
	fmt.Printf("初始化数据库配置耗时: %v\n", time.Since(t5))

	// 创建多个数据库连接
	t6 := time.Now()
	dbService.NewConnection()         // 默认数据库连接
	dbService.NewConnectionWurenche() // 其他数据库连接
	// dbService.NewConnectionJzy()      // jzy 数据库连接
	dbService.NewConnectionCoffee() // coffee 数据库连接
	fmt.Printf("创建多个数据库连接耗时: %v\n", time.Since(t6))

	// 初始化环境
	t7 := time.Now()
	loadEnvironment()
	fmt.Printf("初始化环境耗时: %v\n", time.Since(t7))

	var (
		tcpHost, webHost, appName string
		tcpPort, webPort          int
		keepaliveTimeout          time.Duration
	)
	// 设置应用启动时间
	vars.App.Start = time.Now().Format("2006-01-02 15:04:05")

	// 获取 TCP 主机地址
	if tcpHost = vars.Config.Server.Tcp.Host; tcpHost == "" {
		tcpHost = "0.0.0.0"
	}
	// 获取 TCP 端口
	if tcpPort = vars.Config.Server.Tcp.Port; tcpPort == 0 {
		tcpPort = 2020
	}
	// 获取 TCP 保活超时时间
	if keepaliveTimeout = vars.Config.Server.Tcp.KeepaliveTimeout; keepaliveTimeout == 0 {
		keepaliveTimeout = 150
	}

	// 获取 Web 主机地址
	if webHost = vars.Config.Server.Web.Host; webHost == "" {
		webHost = "0.0.0.0"
	}
	// 获取 Web 端口
	if webPort = vars.Config.Server.Web.Port; webPort == 0 {
		webPort = 8080
	}

	// 获取应用名称
	if appName = vars.Config.App.Name; appName == "" {
		appName = "TCP SERVER 210527"
	}

	// 设置保活超时时间
	keepaliveTimeout *= time.Second
	// 构建 TCP URI
	tcpURI := fmt.Sprintf("%s:%d", tcpHost, tcpPort)
	// 构建 Web URI
	webURI := fmt.Sprintf("%s:%d", webHost, webPort)

	fmt.Printf("服务启动总耗时: %v\n", time.Since(startTotal))

	// 打印服务器启动信息
	var servMsg = `
------------------------------------------------------------------------
------------------------------------------------------------------------
-----------------     %s
--------              TCP  Server: %s
--------              Web  Server: %s
--------              Start Time : %s
--------                  Version: %s
------------------------------------------------------------------------
------------------------------------------------------------------------
`
	fmt.Printf(servMsg, appName, tcpURI, webURI, vars.App.Start, vars.App.Ver)

	// 启动 Web 服务器
	go func() {
		s := g.Server()
		s.SetAddr(webURI)

		// 配置静态文件服务
		s.AddStaticPath("/static", "static")
		s.AddStaticPath("/", "static")  // 支持根路径访问静态文件
		s.SetServerRoot(".")

		// 注册设备状态API路由
		api.RegisterDeviceStatusRoutes(s)

		// 初始化WebSocket服务
		pix_log.Info("[BOOT] 开始初始化WebSocket服务")
		websocket.InitWebSocketService()
		pix_log.Info("[BOOT] WebSocket服务初始化完成")

		// 注册WebSocket路由
		pix_log.Info("[BOOT] 开始注册WebSocket路由")
		api.RegisterWebSocketRoutes(s)
		pix_log.Info("[BOOT] WebSocket路由注册完成")

		pix_log.Info("[WEB] Web服务器启动: %s", webURI)
		if err := s.Start(); err != nil {
			log.Fatalf("[WEB]服务器启动失败: %s", err)
		}
	}()

	// 启动 TCP 服务器
	go func() {
		if e := gtcp.NewServer(tcpURI, func(conn *gtcp.Conn) {
			handleConnection(conn, keepaliveTimeout)
		}, "ROUTER_TCP").Run(); e != nil {
			log.Fatalf("[TCP]服务器启动失败: %s", e)
		}
	}()

	// 启动 Kafka 消费者
	go func() {
		kafkaConsumer() // kafka消费者
	}()

	// 🆕 恢复Dashboard模块
	dashboard.Push()

	// 获取 MQTT 消息
	go func() {
		dashboard.GetMqttMsg()
	}()

	// 启动心跳检测定时任务
	system.StartHeartbeatChecker()

	// 初始化安全防护模块
	security.InitConnectionGuard()

	// ES 模块已删除

	// broadcast
	go func() {
		if e := broadcast.Run(); e != nil {
			log.Fatalf("[BROADCAST]UDP服务器启动失败: %s", e)
		}
	}()

	go doDbRelatedTask(true)

	g.Wait()
	return nil
}

/*
doDbRelatedTask
isStarting true 服务在启动阶段中
*/
func doDbRelatedTask(isStarting bool) {
	db.DoReportRelatedTask(isStarting)
	db.DoAutoClearDbOldDataTask(isStarting)
}

// doRecvAndParse 处理接收和解析终端数据
// 该方法在一个循环中不断接收来自终端连接的数据，并根据接收到的数据执行相应的处理逻辑。
func doRecvAndParse(term *vars.Terminal) {
	// 确保在函数退出时关闭连接
	defer term.Conn.Close()

	for {
		// 从终端连接接收数据
		data, err := term.Conn.Recv(-1)
		if err != nil {
			// 如果接收过程中出现错误，更新终端的连接状态
			term.IsConnected = false

			// 🆕 增强日志：详细记录连接错误原因
			if err == io.EOF { // 终端主动退出
				term.Prop.QuitReason = vars.DISCONNECT_REASON_EOF
				pix_log.InfoWithIMSI(term.Prop.IMSI, "📡 [TCP_RECV] 客户端主动断开连接：IP=%s, 错误=EOF", term.Prop.IP)
			} else if strings.Contains(err.Error(), "use of closed network connection") { // 连接已被关闭
				term.Prop.QuitReason = err.Error()
				pix_log.InfoWithIMSI(term.Prop.IMSI, "📡 [TCP_RECV] 连接已关闭：IP=%s, 错误=%s", term.Prop.IP, err.Error())
			} else if strings.Contains(err.Error(), "timeout") { // 超时
				term.Prop.QuitReason = vars.DISCONNECT_REASON_DETECT
				pix_log.WarningWithIMSI(term.Prop.IMSI, "⏰ [TCP_RECV] 连接超时：IP=%s, 错误=%s, 超时时间=%v",
					term.Prop.IP, err.Error(), term.Prop.KeepaliveTimeout)
			} else { // 其它网络错误
				term.Prop.QuitReason = vars.DISCONNECT_REASON_DETECT
				pix_log.WarningWithIMSI(term.Prop.IMSI, "🔌 [TCP_RECV] 网络连接异常：IP=%s, 错误=%s", term.Prop.IP, err.Error())
			}

			// 🆕 增强日志：记录连接统计信息
			connectionDuration := time.Since(term.Prop.ConnectAt)
			lastUpdateDuration := time.Since(term.Prop.UpdatedAt)
			pix_log.InfoWithIMSI(term.Prop.IMSI, "📊 [TCP_RECV] 连接统计：连接时长=%v, 最后活动=%v前, 授权状态=%t",
				connectionDuration, lastUpdateDuration, term.Prop.Authorized)

			// 通知其他协程终端已退出
			term.Prop.Quit <- true
			flags--
			return
		}

		// 检查接收数据的长度，如果有数据则更新终端的最后更新时间
		if len(data) > 0 {
			term.Prop.UpdatedAt = time.Now()
			// 重置连接的超时时间
			if e := term.Conn.SetDeadline(time.Now().Add(term.Prop.KeepaliveTimeout)); e != nil {
				v.LogTcp().Errorf("[CONNECT]重置有效时间失败: %s", e)
			}
			// 处理接收到的数据，通过调用 term.Parser.HandleRecv(data, term) 方法
			term.Parser.HandleRecv(data, term)
		}
	}
}

// 消息处理过程
func doSendAndHandleDisconnect(term *vars.Terminal) {
	for {
		select {
		case <-term.Prop.Quit: // 主动退出或超时
			term.Mutex.Lock()

			// 🆕 增强日志：记录断开处理的触发
			pix_log.InfoWithIMSI(term.Prop.IMSI, "🔔 [DISCONNECT_HANDLER] 收到断开信号：原因=%s, 连接状态=%t",
				term.Prop.QuitReason, term.IsConnected)

			// DebugOfOffline v.LogTcp().Debugf("终端下线, IMSI:%s, 原因: %s", term.Prop.IMSI, term.Prop.QuitReason)
			removeDeviceFromList(term)
			term.Mutex.Unlock()
			return

		case msg := <-term.Prop.Message: // 消息发送
			go func() {
				if e := term.Conn.Send(msg); e != nil {
					pix_log.Error("reply (%s) error: %s", term.Prop.IMSI, e)
				} else {
					if err := term.Conn.SetDeadline(time.Now().Add(term.Prop.KeepaliveTimeout)); err != nil {
						// pix_log.Error("[CONNECT]重置有效时间失败(MSG): %s", err)
						pix_log.Error("[CONNECT] reset time error (MSG): %s", err)
					} else {
						// var jsonData interface{}
						// if err := json.Unmarshal(msg, &jsonData); err != nil {
						// 	pix_log.Warning("下发消息解析JSON失败: %v, raw data: %s", err, string(msg))
						// } else {
						// 	prettyJSON, _ := json.MarshalIndent(jsonData, "", "  ")
						// 	pix_log.Info("回复 %s 消息 %s: ", term.Prop.IMSI, string(prettyJSON))
						// }
						// pix_log.Info("消息回复 %s 成功：%s", term.Prop.IMSI, term.Prop.Msg)
					}
				}
			}()
		}
	}
}

func DoSendAndHandleDisconnectMqtt(term *vars.Terminal) {
	for {
		select {
		case <-term.Prop.Quit: // 主动退出或超时
			term.Mutex.Lock()
			// DebugOfOffline v.LogTcp().Debugf("终端下线, IMSI:%s, 原因: %s", term.Prop.IMSI, term.Prop.QuitReason)
			removeDeviceFromList(term)
			term.Mutex.Unlock()
			return

		case msg := <-term.Prop.Message: // 消息发送
			go func() {
				if mqtt.SharedClient.IsConnected() {
					// token1 := mqtt.SharedClient.Subscribe(topic, 0, nil)
					// token1.Wait()
					// 🆕 直接使用MQTT配置
					topic := fmt.Sprintf("%s/mqtt/server/%s",
						vars.Config.Server.Mqtt.TopicPrefix,
						term.Prop.IMSI,
					)
					token := mqtt.SharedClient.Publish(topic, 0, false, string(msg))
					token.Wait() // wait是个阻塞函数，只有操作完成时才能返回
					// token.WaitTimeout(15 * time.Second)

					if token.Error() != nil {
						v.Log().Debugf("mqtt publish failed:%s", token.Error())
					}
				} else {
					v.Log().Warningf("mqtt not connected, ignore publish")
				}
			}()
		}
	}
}

// 将终端从内存中缓存的map列表中移除
func removeDeviceFromList(term *vars.Terminal) {
	defer term.Conn.Close() // 关闭客户端

	// 🆕 增强日志：记录设备移除的详细信息
	pix_log.InfoWithIMSI(term.Prop.IMSI, "🔄 [DISCONNECT] 开始处理设备断开：IP=%s, 连接地址=%s, 退出原因=%s, 授权状态=%t",
		term.Prop.IP, term.Conn.RemoteAddr(), term.Prop.QuitReason, term.Prop.Authorized)

	// 判断内存中缓存的map列表中是否已存在此IMEI终端
	if c := client.Get(term.Prop.IMSI); c != nil {
		cachedTerm := c.(*vars.Terminal)

		// 🆕 增强日志：记录缓存中的设备信息
		pix_log.InfoWithIMSI(term.Prop.IMSI, "📋 [DISCONNECT] 缓存中存在设备：缓存连接=%v, 当前连接=%v, 缓存地址=%s, 当前地址=%s",
			&cachedTerm.Conn, &term.Conn, cachedTerm.Conn.RemoteAddr(), term.Conn.RemoteAddr())

		// map存的终端与要移除的终端连接相同,则移除map存的终端
		if &(cachedTerm.Conn) == &(term.Conn) {
			// 🆕 增强日志：记录正常移除
			pix_log.InfoWithIMSI(term.Prop.IMSI, "✅ [DISCONNECT] 正常移除设备：连接匹配，原因=%s", term.Prop.QuitReason)
			v.LogTcp().Debugf("[CLIENTS]列表中移除终端(%s)连接: %v, Addr: %s", term.Prop.IMSI, &term.Conn, term.Conn.RemoteAddr())

			client.Del(term.Prop.IMSI) // 移除终端
			// 删除Redis中的心跳时间记录
			cacheService.DelRedisHeartbeatTs(term.Prop.IMSI)
			dbService.RecordDeviceOffline(term, "终端下线, 原因:"+term.Prop.QuitReason)

			// 发布设备断开连接分析消息
			if err := analysis.SharedPublisher.PublishDeviceMessage(term.Prop.IMSI, "disconnect"); err != nil {
				pix_log.ErrorWithIMSI(term.Prop.IMSI, "发布断开连接分析消息失败: %v", err)
			}
		} else { // map存的终端与要移除的终端连接不相同,说明终端异常断开连接(如突然断电)后又连接或有重复的IMEI设备上线
			// 🆕 增强日志：记录连接不匹配的情况
			pix_log.WarningWithIMSI(term.Prop.IMSI, "⚠️ [DISCONNECT] 连接不匹配，疑似重复IMSI：缓存连接=%v, 当前连接=%v, 原因=%s",
				&cachedTerm.Conn, &term.Conn, term.Prop.QuitReason)
			v.LogTcp().Debugf("终端下线(连接不同，IMSI可能重复), IMSI:" + term.Prop.IMSI + "原因:" + term.Prop.QuitReason)
			// 不在登录日志表中记录此种情况的下线
			// dbService.RecordDeviceOffline(term, "终端下线(连接不同，IMSI可能重复), 原因:" + term.Prop.QuitReason)
		}
	} else { // 不存在此终端。若IMSI为""，则说明此前此term未注册成功，IMSI不为""的情况应该不存在
		// 🆕 增强日志：记录缓存中不存在的情况
		if term.Prop.IMSI == "" {
			pix_log.InfoWithIMSI("N/A", "📝 [DISCONNECT] 未注册设备断开：IP=%s, 连接地址=%s, 原因=%s",
				term.Prop.IP, term.Conn.RemoteAddr(), term.Prop.QuitReason)
		} else {
			pix_log.WarningWithIMSI(term.Prop.IMSI, "⚠️ [DISCONNECT] 已注册设备不在缓存中：IP=%s, 连接地址=%s, 原因=%s",
				term.Prop.IP, term.Conn.RemoteAddr(), term.Prop.QuitReason)
		}
		v.LogTcp().Debugf("终端下线, 终端未在缓存列表中, IMSI:"+term.Prop.IMSI+", 原因:"+term.Prop.QuitReason+", term:%x", term)
		dbService.RecordDeviceOffline(term, "终端下线, 终端未在缓存列表中, IMSI:"+term.Prop.IMSI+", 原因:"+term.Prop.QuitReason) // 离线终端
	}

	url := "https://api.thousandsim.com/open/api?method=qiancen.rop.vehicle.alarm.report"
	lastHbJsonStr := cacheService.GetRedisDeviceHeartbeat(term.Prop.IMSI)
	if len(lastHbJsonStr) == 0 || lastHbJsonStr == "" {
		// do nothing
	} else {
		j, err := gjson.DecodeToJson(lastHbJsonStr)
		if err != nil {
			v.LogTcp().Errorf("[createRespDeviceData] 数据解析出错:%s", err)
		} else {
			lat := j.GetFloat64("lat")
			lng := j.GetFloat64("lng")
			body := map[string]interface{}{
				"vehicleId": fmt.Sprintf("%d", term.Prop.ID),
				"location": map[string]interface{}{
					"lon": lng,
					"lat": lat,
				},
				"code":    "0X0002",
				"message": term.Prop.QuitReason,
			}
			PostData(body, url)
		}
	}
}

// PostData 杭州项目
func PostData(body map[string]interface{}, url string) {
	stringBody, _ := json.Marshal(body)

	signature := common.GenerateHmacSHA256Signature("8WzjFegQ5xJlaHF3", string(stringBody))

	// 创建一个新的请求
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(stringBody))
	if err != nil {
		pix_log.Error("Error creating request: %v", err)
		return
	}

	// 添加自定义Header
	req.Header.Set("Content-Type", "application/json;charset=utf-8")
	req.Header.Set("x-auth-key", "9zjn")
	req.Header.Set("x-auth-signature", signature)

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		pix_log.Error("Error sending request: %v", err)
		return
	}
	defer resp.Body.Close()

	// 读取响应数据
	// responseBody, err := ioutil.ReadAll(resp.Body)
	_, err = ioutil.ReadAll(resp.Body)
	if err != nil {
		pix_log.Error("Error reading response:", err)
		return
	}

	// 打印响应数据
	// pix_log.Info("Response:", string(responseBody))
}
