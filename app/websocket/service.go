package websocket

import (
	"ccserver/app/service/tdengine"
	"ccserver/app/vars"
	"ccserver/pix_log"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/gorilla/websocket"
	"github.com/google/uuid"
)

// ClientState 客户端状态
type ClientState struct {
	SessionID    string                 `json:"session_id"`
	CurrentPage  int                    `json:"current_page"`
	PageSize     int                    `json:"page_size"`
	Filters      map[string]interface{} `json:"filters"`
	LastUpdate   time.Time              `json:"last_update"`
	Connection   *websocket.Conn        `json:"-"`
}

// WebSocketService WebSocket服务
type WebSocketService struct {
	clients        map[*websocket.Conn]*ClientState
	clientsMutex   sync.RWMutex
	tdengineClient *tdengine.TDengineClient
	stopChan       chan bool
	isRunning      bool
}

// NewWebSocketService 创建WebSocket服务
func NewWebSocketService() *WebSocketService {
	pix_log.Info("[WEBSOCKET] 创建WebSocket服务实例")

	// 创建TDengine客户端
	client := tdengine.NewTDengineClient()
	pix_log.Info("[WEBSOCKET] TDengine客户端创建成功")

	service := &WebSocketService{
		clients:        make(map[*websocket.Conn]*ClientState),
		tdengineClient: client,
		stopChan:       make(chan bool),
		isRunning:      false,
	}

	pix_log.Info("[WEBSOCKET] WebSocket服务实例创建完成")
	return service
}

// AddClient 添加客户端连接
func (s *WebSocketService) AddClient(conn *websocket.Conn) {
	s.clientsMutex.Lock()
	defer s.clientsMutex.Unlock()

	// 创建新的客户端状态
	sessionID := uuid.New().String()
	clientState := &ClientState{
		SessionID:   sessionID,
		CurrentPage: 1,
		PageSize:    10,
		Filters:     make(map[string]interface{}),
		LastUpdate:  time.Now(),
		Connection:  conn,
	}

	s.clients[conn] = clientState
	pix_log.Info("[WEBSOCKET] 新客户端连接，SessionID: %s，当前连接数: %d", sessionID, len(s.clients))
}

// RemoveClient 移除客户端连接
func (s *WebSocketService) RemoveClient(conn *websocket.Conn) {
	s.clientsMutex.Lock()
	defer s.clientsMutex.Unlock()

	if clientState, exists := s.clients[conn]; exists {
		pix_log.Info("[WEBSOCKET] 客户端断开连接，SessionID: %s，当前连接数: %d", clientState.SessionID, len(s.clients)-1)
		delete(s.clients, conn)
		conn.Close()
	}
}

// GetClientCount 获取客户端连接数
func (s *WebSocketService) GetClientCount() int {
	s.clientsMutex.RLock()
	defer s.clientsMutex.RUnlock()
	return len(s.clients)
}

// SendMessageToClient 发送消息给指定客户端
func (s *WebSocketService) SendMessageToClient(conn *websocket.Conn, messageType string, data interface{}) {
	message := map[string]interface{}{
		"type":      messageType,
		"data":      data,
		"timestamp": time.Now().Unix(),
	}

	messageBytes, err := json.Marshal(message)
	if err != nil {
		pix_log.Error("[WEBSOCKET] 序列化消息失败: %v", err)
		return
	}

	err = conn.WriteMessage(websocket.TextMessage, messageBytes)
	if err != nil {
		pix_log.Error("[WEBSOCKET] 发送消息失败: %v", err)
		s.RemoveClient(conn)
	}
}

// BroadcastMessage 广播消息给所有客户端
func (s *WebSocketService) BroadcastMessage(messageType string, data interface{}) {
	s.clientsMutex.RLock()
	defer s.clientsMutex.RUnlock()

	message := map[string]interface{}{
		"type":      messageType,
		"data":      data,
		"timestamp": time.Now().Unix(),
	}

	messageBytes, err := json.Marshal(message)
	if err != nil {
		pix_log.Error("[WEBSOCKET] 序列化消息失败: %v", err)
		return
	}

	// 记录需要移除的连接
	var toRemove []*websocket.Conn

	for conn := range s.clients {
		err := conn.WriteMessage(websocket.TextMessage, messageBytes)
		if err != nil {
			pix_log.Error("[WEBSOCKET] 发送消息失败: %v", err)
			toRemove = append(toRemove, conn)
		}
	}

	// 移除失效的连接
	for _, conn := range toRemove {
		s.RemoveClient(conn)
	}

	if len(toRemove) == 0 {
		pix_log.Info("[WEBSOCKET] 广播消息成功，类型: %s，接收者: %d", messageType, len(s.clients))
	}
}

// SendDeviceListUpdate 发送设备列表更新（根据客户端状态）
func (s *WebSocketService) SendDeviceListUpdate() {
	s.clientsMutex.RLock()
	defer s.clientsMutex.RUnlock()

	// 获取所有设备数据
	allDevices, err := s.tdengineClient.GetAllDevicesStatus()
	if err != nil {
		pix_log.Error("[WEBSOCKET] 获取设备列表失败: %v", err)
		return
	}

	// 计算统计信息
	stats := s.calculateStats(allDevices)
	totalDevices := len(allDevices)

	// 为每个客户端发送对应页面的数据
	var toRemove []*websocket.Conn
	for conn, clientState := range s.clients {
		// 根据客户端状态计算分页数据
		pageDevices := s.getPagedDevices(allDevices, clientState.CurrentPage, clientState.PageSize, clientState.Filters)

		response := map[string]interface{}{
			"devices":     pageDevices,
			"stats":       stats,
			"total":       totalDevices,
			"current_page": clientState.CurrentPage,
			"page_size":   clientState.PageSize,
			"session_id":  clientState.SessionID,
		}

		err := conn.WriteJSON(map[string]interface{}{
			"type":      "device_list_update",
			"data":      response,
			"timestamp": time.Now().Unix(),
		})

		if err != nil {
			pix_log.Error("[WEBSOCKET] 发送消息失败: %v", err)
			toRemove = append(toRemove, conn)
		}
	}

	// 移除失效的连接
	for _, conn := range toRemove {
		s.RemoveClient(conn)
	}

	if len(toRemove) == 0 && len(s.clients) > 0 {
		pix_log.Info("[WEBSOCKET] 设备列表更新推送完成，接收者: %d", len(s.clients))
	}
}

// SendDeviceDetailUpdate 发送设备详情更新
func (s *WebSocketService) SendDeviceDetailUpdate(imsi string) {
	tableName := fmt.Sprintf("device_heartbeat_%s", imsi)
	device, err := s.tdengineClient.GetLatestDeviceStatus(tableName)
	if err != nil {
		pix_log.Error("[WEBSOCKET] 获取设备详情失败: %v", err)
		return
	}

	s.BroadcastMessage("device_detail_update", map[string]interface{}{
		"imsi":   imsi,
		"device": device,
	})
}

// SendDeviceHistoryUpdate 发送设备历史更新
func (s *WebSocketService) SendDeviceHistoryUpdate(imsi string, limit int) {
	// 获取最近24小时的历史数据
	endTime := time.Now()
	startTime := endTime.Add(-24 * time.Hour)
	
	history, err := s.tdengineClient.GetDeviceHistory(imsi, startTime, endTime, limit)
	if err != nil {
		pix_log.Error("[WEBSOCKET] 获取设备历史失败: %v", err)
		return
	}

	s.BroadcastMessage("device_history_update", map[string]interface{}{
		"imsi":    imsi,
		"history": history,
		"total":   len(history),
	})
}

// calculateStats 计算设备统计信息
func (s *WebSocketService) calculateStats(devices []*tdengine.DeviceStatus) map[string]interface{} {
	totalDevices := len(devices)
	onlineDevices := 0
	offlineDevices := 0
	errorDevices := 0

	// 离线阈值（5分钟）
	offlineThreshold := time.Now().Add(-5 * time.Minute)

	for _, device := range devices {
		// 判断设备状态 - device.LastHeartbeatTime 已经是 time.Time 类型
		if device.LastHeartbeatTime.After(offlineThreshold) {
			onlineDevices++
		} else {
			offlineDevices++
		}

		// 检查错误状态（这里可以根据具体业务逻辑调整）
		if device.ErrorCount > 0 {
			errorDevices++
		}
	}

	onlineRate := 0.0
	offlineRate := 0.0
	if totalDevices > 0 {
		onlineRate = float64(onlineDevices) / float64(totalDevices) * 100
		offlineRate = float64(offlineDevices) / float64(totalDevices) * 100
	}

	return map[string]interface{}{
		"total_devices":   totalDevices,
		"online_devices":  onlineDevices,
		"offline_devices": offlineDevices,
		"error_devices":   errorDevices,
		"online_rate":     onlineRate,
		"offline_rate":    offlineRate,
	}
}

// getPagedDevices 获取分页设备数据
func (s *WebSocketService) getPagedDevices(allDevices []*tdengine.DeviceStatus, page, pageSize int, filters map[string]interface{}) []*tdengine.DeviceStatus {
	// 应用过滤条件
	filteredDevices := s.applyFilters(allDevices, filters)

	// 计算分页
	totalDevices := len(filteredDevices)
	startIndex := (page - 1) * pageSize
	endIndex := startIndex + pageSize

	if startIndex >= totalDevices {
		return []*tdengine.DeviceStatus{}
	}

	if endIndex > totalDevices {
		endIndex = totalDevices
	}

	return filteredDevices[startIndex:endIndex]
}

// applyFilters 应用过滤条件
func (s *WebSocketService) applyFilters(devices []*tdengine.DeviceStatus, filters map[string]interface{}) []*tdengine.DeviceStatus {
	if len(filters) == 0 {
		return devices
	}

	var filtered []*tdengine.DeviceStatus
	offlineThreshold := time.Now().Add(-5 * time.Minute)

	for _, device := range devices {
		include := true

		// 状态过滤
		if statusFilter, exists := filters["status"]; exists && statusFilter != "" && statusFilter != "all" {
			isOnline := device.LastHeartbeatTime.After(offlineThreshold)
			if statusFilter == "online" && !isOnline {
				include = false
			} else if statusFilter == "offline" && isOnline {
				include = false
			}
		}

		// IMSI搜索过滤
		if searchFilter, exists := filters["search"]; exists && searchFilter != "" {
			searchStr := searchFilter.(string)
			if searchStr != "" && !contains(device.IMSI, searchStr) {
				include = false
			}
		}

		if include {
			filtered = append(filtered, device)
		}
	}

	return filtered
}

// contains 检查字符串是否包含子字符串（忽略大小写）
func contains(str, substr string) bool {
	return len(str) >= len(substr) &&
		   (substr == "" ||
		    str == substr ||
		    len(str) > len(substr) && (str[:len(substr)] == substr ||
		    str[len(str)-len(substr):] == substr ||
		    findInString(str, substr)))
}

// findInString 在字符串中查找子字符串
func findInString(str, substr string) bool {
	for i := 0; i <= len(str)-len(substr); i++ {
		if str[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}

// UpdateClientState 更新客户端状态
func (s *WebSocketService) UpdateClientState(conn *websocket.Conn, page, pageSize int, filters map[string]interface{}) {
	s.clientsMutex.Lock()
	defer s.clientsMutex.Unlock()

	if clientState, exists := s.clients[conn]; exists {
		clientState.CurrentPage = page
		clientState.PageSize = pageSize
		clientState.Filters = filters
		clientState.LastUpdate = time.Now()

		pix_log.Info("[WEBSOCKET] 客户端状态更新，SessionID: %s，页码: %d，每页: %d",
			clientState.SessionID, page, pageSize)
	}
}

// Start 启动WebSocket服务
func (s *WebSocketService) Start() {
	if s.isRunning {
		pix_log.Info("[WEBSOCKET] 服务已经在运行中")
		return
	}

	s.isRunning = true
	pix_log.Info("[WEBSOCKET] 启动WebSocket服务")

	// 启动定时推送
	go s.startPeriodicUpdates()
}

// Stop 停止WebSocket服务
func (s *WebSocketService) Stop() {
	if !s.isRunning {
		return
	}

	s.isRunning = false
	close(s.stopChan)

	// 关闭所有客户端连接
	s.clientsMutex.Lock()
	defer s.clientsMutex.Unlock()

	for conn := range s.clients {
		conn.Close()
	}
	s.clients = make(map[*websocket.Conn]*ClientState)

	pix_log.Info("[WEBSOCKET] WebSocket服务已停止")
}

// startPeriodicUpdates 启动定时更新
func (s *WebSocketService) startPeriodicUpdates() {
	// 从配置中获取推送间隔，默认30秒
	interval := vars.Config.Business.PushInterval
	if interval == 0 {
		interval = 30 * time.Second
	}

	ticker := time.NewTicker(interval)
	defer ticker.Stop()

	pix_log.Info("[WEBSOCKET] 启动定时推送，间隔: %v", interval)

	for {
		select {
		case <-ticker.C:
			if s.GetClientCount() > 0 {
				s.SendDeviceListUpdate()
			}
		case <-s.stopChan:
			pix_log.Info("[WEBSOCKET] 停止定时推送")
			return
		}
	}
}

// HandleMessage 处理客户端消息
func (s *WebSocketService) HandleMessage(conn *websocket.Conn, messageType int, data []byte) {
	var message map[string]interface{}
	if err := json.Unmarshal(data, &message); err != nil {
		pix_log.Error("[WEBSOCKET] 解析客户端消息失败: %v", err)
		return
	}

	msgType, ok := message["type"].(string)
	if !ok {
		pix_log.Error("[WEBSOCKET] 消息类型无效")
		return
	}

	pix_log.Info("[WEBSOCKET] 收到客户端消息，类型: %s", msgType)

	switch msgType {
	case "get_device_list":
		s.SendDeviceListUpdate()
	case "client_state_update":
		// 更新客户端状态
		if data, ok := message["data"].(map[string]interface{}); ok {
			page := 1
			pageSize := 10
			filters := make(map[string]interface{})

			if p, ok := data["page"].(float64); ok {
				page = int(p)
			}
			if ps, ok := data["page_size"].(float64); ok {
				pageSize = int(ps)
			}
			if f, ok := data["filters"].(map[string]interface{}); ok {
				filters = f
			}

			s.UpdateClientState(conn, page, pageSize, filters)

			// 立即发送更新后的数据
			s.SendDeviceListUpdateToClient(conn)
		}
	case "get_device_detail":
		if imsi, ok := message["imsi"].(string); ok {
			s.SendDeviceDetailUpdate(imsi)
		}
	case "get_device_history":
		if imsi, ok := message["imsi"].(string); ok {
			limit := 100 // 默认限制
			if l, ok := message["limit"].(float64); ok {
				limit = int(l)
			}
			s.SendDeviceHistoryUpdate(imsi, limit)
		}
	case "ping":
		// 响应ping消息
		response := map[string]interface{}{
			"type":      "pong",
			"timestamp": time.Now().Unix(),
		}
		responseBytes, _ := json.Marshal(response)
		conn.WriteMessage(websocket.TextMessage, responseBytes)
	default:
		pix_log.Info("[WEBSOCKET] 未知消息类型: %s", msgType)
	}
}

// SendDeviceListUpdateToClient 发送设备列表更新给指定客户端
func (s *WebSocketService) SendDeviceListUpdateToClient(conn *websocket.Conn) {
	s.clientsMutex.RLock()
	clientState, exists := s.clients[conn]
	s.clientsMutex.RUnlock()

	if !exists {
		return
	}

	// 获取所有设备数据
	allDevices, err := s.tdengineClient.GetAllDevicesStatus()
	if err != nil {
		pix_log.Error("[WEBSOCKET] 获取设备列表失败: %v", err)
		return
	}

	// 计算统计信息
	stats := s.calculateStats(allDevices)
	totalDevices := len(allDevices)

	// 根据客户端状态计算分页数据
	pageDevices := s.getPagedDevices(allDevices, clientState.CurrentPage, clientState.PageSize, clientState.Filters)

	response := map[string]interface{}{
		"devices":     pageDevices,
		"stats":       stats,
		"total":       totalDevices,
		"current_page": clientState.CurrentPage,
		"page_size":   clientState.PageSize,
		"session_id":  clientState.SessionID,
	}

	err = conn.WriteJSON(map[string]interface{}{
		"type":      "device_list_update",
		"data":      response,
		"timestamp": time.Now().Unix(),
	})

	if err != nil {
		pix_log.Error("[WEBSOCKET] 发送消息失败: %v", err)
		s.RemoveClient(conn)
	} else {
		pix_log.Info("[WEBSOCKET] 设备列表更新发送成功，SessionID: %s，页码: %d",
			clientState.SessionID, clientState.CurrentPage)
	}
}

// 全局WebSocket服务实例
var GlobalWebSocketService *WebSocketService

// InitWebSocketService 初始化WebSocket服务
func InitWebSocketService() {
	pix_log.Info("[WEBSOCKET] 开始初始化WebSocket服务")
	GlobalWebSocketService = NewWebSocketService()
	GlobalWebSocketService.Start()
	pix_log.Info("[WEBSOCKET] WebSocket服务初始化完成")
}
