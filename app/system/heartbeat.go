package system

import (
	"bytes"
	"ccserver/app/library/common"
	"ccserver/app/library/v"
	"ccserver/app/model"
	"ccserver/app/module/mqtt"
	"ccserver/app/security"
	cacheservice "ccserver/app/service/cache"
	"ccserver/app/service/db"
	"ccserver/app/vars"
	"ccserver/app/vars/protocol"
	"ccserver/pix_log"
	"ccserver/pkg"
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"strconv"
	"time"

	"github.com/gogf/gf/encoding/gjson"
	"github.com/gogf/gf/frame/g"
	"github.com/jinzhu/copier"
	"gorm.io/gorm"
)

type TaskContent struct {
	Action  string `json:"action"`
	Content string `json:"content"`
	PET     int    `json:"pET"`
	PST     int    `json:"pST"`
}

// Heartbeat 心跳
type Heartbeat struct {
	base
	term            *vars.Terminal
	data            []byte
	ts              int64
	heartbeatDevice *protocol.HeartbeatDevice
}

// ToHeartbeatMqtt 设备数据转换为MQTT数据
func (t *Heartbeat) ToHeartbeatMqtt() (*protocol.HeartbeatMqtt, error) {
	heartbeatMqtt := protocol.NewHeartbeatMqtt(t.ts)

	if err := copier.CopyWithOption(heartbeatMqtt, t.heartbeatDevice, pkg.GetCopierOption()); err != nil {
		fmt.Println("转换MQTT数据失败:", err.Error())
		pix_log.Error("转换MQTT数据失败:", err.Error())
		return nil, err
	}

	return heartbeatMqtt, nil
}

// ToHeartbeatRedis 设备数据转换为Redis数据
func (t *Heartbeat) ToHeartbeatRedis() (*protocol.HeartbeatRedis, error) {
	redis := protocol.NewHeartbeatRedis(t.ts)

	if err := copier.CopyWithOption(redis, t.heartbeatDevice, pkg.GetCopierOption()); err != nil {
		fmt.Println("转换Redis数据失败:", err.Error())
		pix_log.Error("转换Redis数据失败:", err.Error())
		return nil, err
	}

	return redis, nil
}

// ToHeartbeatRecord 设备数据转为数据库记录
func (t *Heartbeat) ToHeartbeatRecord(lan1Status, lan2Status int64) (*protocol.HeartbeatRecord, error) {
	// 创建目标对象
	record := protocol.NewHeartbeatRecord()

	// 打印源数据的 JSON 格式
	// srcJSON, err := json.MarshalIndent(t.heartbeatDevice, "", "  ")
	// if err != nil {
	// 	pix_log.Error("源数据转换为 JSON 失败:", err.Error())
	// 	return nil, err
	// }
	// pix_log.Info("源数据 (heartbeatDevice):\n", string(srcJSON))

	// 复制基础字段
	if err := copier.CopyWithOption(record, t.heartbeatDevice, pkg.GetCopierOption()); err != nil {
		pix_log.Error("转换MQTT记录数据失败:", err.Error())
		return nil, err
	}

	// 设置基本标识字段
	record.DeviceID = t.term.Prop.ID
	record.UID = t.term.Prop.UID
	record.Lan1Status = lan1Status
	record.Lan2Status = lan2Status
	record.Ts = time.Now().Unix()

	// 打印目标数据的 JSON 格式
	// dstJSON, err := json.MarshalIndent(record, "", "  ")
	// if err != nil {
	// 	pix_log.Error("目标数据转换为 JSON 失败:", err.Error())
	// 	return nil, err
	// }
	// pix_log.Info("目标数据 (HeartbeatRecord):\n", string(dstJSON))

	return record, nil
}

// NewHeartbeat 心跳初始化
func NewHeartbeat(device *protocol.HeartbeatDevice, term *vars.Terminal, d []byte, ts int64) *Heartbeat {
	// 🆕 添加心跳处理器创建层调试日志
	pix_log.InfoWithIMSI(device.IMSI, "🏗️ [NewHeartbeat] 创建心跳处理器：Step=%d, TaskId=%d, UID=%d",
		device.LatestTask.Step, device.LatestTask.TaskID, term.Prop.UID)

	// 更新终端属性
	term.Prop.UpgradeState = device.UpgradeState
	term.Prop.UpgradeProgress = device.UpgradeProgress

	// 返回心跳对象
	heartbeat := &Heartbeat{
		term:            term,
		data:            d,
		ts:              ts,
		heartbeatDevice: device,
	}

	pix_log.InfoWithIMSI(device.IMSI, "✅ [NewHeartbeat] 心跳处理器创建完成")
	return heartbeat
}

// PostData 杭州项目
func (t *Heartbeat) PostData(body map[string]interface{}, url string) {
	stringBody, _ := json.Marshal(body)

	signature := common.GenerateHmacSHA256Signature("8WzjFegQ5xJlaHF3", string(stringBody))

	// 创建一个新的请求
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(stringBody))
	if err != nil {
		pix_log.Error("Error creating request:", err)
		return
	}

	// 添加自定义Header
	req.Header.Set("Content-Type", "application/json;charset=utf-8")
	req.Header.Set("x-auth-key", "9zjn")
	req.Header.Set("x-auth-signature", signature)

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		pix_log.Error("Error sending request:", err)
		return
	}
	defer resp.Body.Close()

	// 读取响应数据
	_, err = ioutil.ReadAll(resp.Body)
	if err != nil {
		pix_log.Error("Error reading response: %v", err)
		return
	}

	// 打印响应数据
	// pix_log.Info("杭州 Response:", string(responseBody))
}

// isNeededRecord 是否需要记录到数据库中
func (t *Heartbeat) isNeededRecord() bool {
	pix_log.InfoWithIMSI(t.term.Prop.IMSI, "🔍 检查设备是否需要记录，UID=%d", t.term.Prop.UID)

	if t.term.Prop.UID == 0 { // 未设置归属者，无需添加记录到数据库
		pix_log.WarningWithIMSI(t.term.Prop.IMSI, "❌ 设备UID为0，跳过数据库记录")
		return false
	}

	// 🎯 简化逻辑：默认所有心跳都需要记录到数据库
	// 移除了时间间隔限制，确保数据完整性
	pix_log.InfoWithIMSI(t.term.Prop.IMSI, "✅ 设备需要记录到数据库")
	return true
}

// GetReply 获取回复
func (t *Heartbeat) GetReply(billNo uint32) ([]byte, error) {
	// 🆕 使用带IMSI的日志函数
	pix_log.TcpInfoWithIMSI(t.term.Prop.IMSI, "🚀 [GetReply] 开始处理心跳：Step=%d, TaskId=%d, Authorized=%t",
		t.heartbeatDevice.LatestTask.Step, t.heartbeatDevice.LatestTask.TaskID, t.term.Prop.Authorized)

	var res int
	replyData := g.Map{
		"cmd": protocol.CbHeartbeat,
		"res": res,
		"bn":  billNo,
	}

	if !t.term.Prop.Authorized {
		// 增强日志：记录未授权设备的详细信息和可能原因
		pix_log.WarningWithIMSI(t.term.Prop.IMSI, "⚠️ [GetReply] 设备未授权，跳过心跳处理：IP=%s, Step=%d, TaskId=%d",
			t.term.Prop.IP, t.heartbeatDevice.LatestTask.Step, t.heartbeatDevice.LatestTask.TaskID)
		pix_log.WarningWithIMSI(t.term.Prop.IMSI, "💡 [GetReply] 可能原因：1) 设备未在device表中注册 2) 设备已过期 3) 注册过程失败")

		return gjson.Encode(replyData)
	}

	// ---------- redis操作逻辑 begin ----------
	// 构建缓存数据
	redis, err2 := t.ToHeartbeatRedis()
	if err2 != nil {
		return nil, err2
	}

	// 序列化缓存数据
	cacheJSON, err := json.Marshal(redis)
	if err != nil {
		pix_log.ErrorWithIMSI(t.term.Prop.IMSI, "序列化缓存数据失败: %v", err)
	} else {
		// 打印日志
		// fmt.Println("心跳消息写入redis：", string(cacheJSON))
		// pix_log.Info("心跳消息写入redis：", string(cacheJSON))

		// 存入 Redis
		cacheservice.SetRedisDeviceHeartbeat(t.term.Prop.IMSI, string(cacheJSON))
	}

	cacheservice.SetRedisHeartbeatTs(t.term.Prop.IMSI, time.Now().Unix())
	// ---------- redis操作逻辑 end ----------

	// ---------- mqtt数据推送逻辑 begin (暂不知道哪里使用这个主题）----------

	// 🆕 直接使用MQTT配置
	topic := fmt.Sprintf("%s/%s/sync",
		vars.Config.Server.Mqtt.TopicPrefix,
		t.term.Prop.IMSI,
	)
	// 使用 toHeartbeatPayload 方法获取负载数据
	payload, err2 := t.ToHeartbeatMqtt()
	if err2 != nil {
		return nil, err2
	}
	payloadJSON, _ := gjson.Encode(payload)

	// 调试打印
	// var prettyJSON bytes.Buffer
	// err := json.Indent(&prettyJSON, payloadJSON, "", "    ")
	// if err != nil {
	// 	return nil, err
	// }
	// pix_log.Info("心跳数据:\n", prettyJSON.String())
	// fmt.Println("心跳数据:\n", prettyJSON.String())

	token := mqtt.SharedClient.Publish(topic, 0, false, string(payloadJSON))
	token.Wait()
	// ---------- mqtt数据推送逻辑 end ----------

	// ---------- 心跳数据入库逻辑 begin ----------
	// 🆕 添加isNeededRecord调用前的日志
	pix_log.InfoWithIMSI(t.term.Prop.IMSI, "🔍 [GetReply] 准备检查是否需要记录：Step=%d, TaskId=%d",
		t.heartbeatDevice.LatestTask.Step, t.heartbeatDevice.LatestTask.TaskID)

	isNeededRecord := t.isNeededRecord()

	// 🆕 添加isNeededRecord调用后的日志
	pix_log.InfoWithIMSI(t.term.Prop.IMSI, "📋 [GetReply] isNeededRecord结果：需要记录=%t", isNeededRecord)

	if isNeededRecord {
		pix_log.InfoWithIMSI(t.term.Prop.IMSI, "🔄 [GetReply] 开始执行processing")
		if err := t.processing(); err != nil {
			pix_log.ErrorWithIMSI(t.term.Prop.IMSI, "❌ [GetReply] processing执行失败：错误=%v", err)
			return nil, err
		}
		pix_log.InfoWithIMSI(t.term.Prop.IMSI, "✅ [GetReply] processing执行成功")
	} else {
		pix_log.InfoWithIMSI(t.term.Prop.IMSI, "⏭️ [GetReply] 跳过processing")
	}
	// ---------- 心跳数据入库逻辑  ----------

	// ---------- 数据分析通知逻辑 begin ----------
	// if err := analysis.SharedPublisher.PublishDeviceMessage(t.term.Prop.IMSI, "heartbeat"); err != nil {
	// 	pix_log.Error("发布心跳分析消息失败:", err.Error())
	// }
	// ---------- 数据分析通知逻辑 end ----------

	return gjson.Encode(replyData)
}

// handleDeviceStatusUpdate 处理设备状态更新
func (t *Heartbeat) handleDeviceStatusUpdate() error {
	updates := make(map[string]interface{})

	// 1. 处理告警状态
	deviceStatus := 0
	if len(t.heartbeatDevice.AlarmCount1) > 0 {
		deviceStatus = 1
	} else {
		deviceStatus = 0
	}
	updates["status"] = deviceStatus

	// 2. 处理驾驶模式
	updates["mode"] = t.heartbeatDevice.Mode

	// 3. 处理自驾定位状态
	updates["ad_located"] = t.heartbeatDevice.ADLocated

	// 🎯 移除在线状态更新逻辑 - 统一由心跳检测器负责
	// 心跳处理器只负责更新Redis时间戳，不再更新数据库online字段

	// 5. 执行数据库更新
	if err := db.GetPixmoving().Table("device").
		Where("imsi = ?", t.term.Prop.IMSI).
		Updates(updates).Error; err != nil {
		pix_log.ErrorWithIMSI(t.term.Prop.IMSI, "更新设备状态失败: %v", err)
		return err
	}

	return nil
}

// 🗑️ 已移除 handleOnlineStatusUpdate 函数
// 在线状态更新统一由心跳检测器负责，避免重复逻辑和竞态条件

func (t *Heartbeat) processing() error {
	// 🆕 安全检查：验证设备授权状态
	if !t.term.Prop.Authorized {
		errorMsg := fmt.Sprintf("心跳处理失败：设备未授权，IMSI=%s", t.term.Prop.IMSI)
		pix_log.Warning(errorMsg)

		// 🆕 记录到安全防护系统
		security.RecordFailedConnection(t.term.Prop.IMSI, t.term.Prop.IP, "心跳处理时设备未授权")

		// 记录安全事件并断开连接
		t.recordSecurityEvent("UNAUTHORIZED_DEVICE_HEARTBEAT", errorMsg)
		t.disconnectUnauthorizedDevice("心跳处理：设备未授权")

		return fmt.Errorf("设备未授权，连接已断开：IMSI=%s", t.term.Prop.IMSI)
	}

	// 🗑️ 移除心跳记录时间戳更新，因为现在所有心跳都会记录

	// 1. 处理 LAN 口状态
	var lan1Status, lan2Status int64
	for _, lanStatus := range t.heartbeatDevice.Lan {
		switch lanStatus.Name {
		case "LAN1":
			lan1Status = lanStatus.LinkStatus
			t.UpdateLan1OfflineTime(t.term.Prop.IMSI, lan1Status, time.Now().Unix())
		case "LAN2":
			lan2Status = lanStatus.LinkStatus
			t.UpdateLan2OfflineTime(t.term.Prop.IMSI, lan2Status, time.Now().Unix())
		}
	}

	// 1.1 处理设备状态更新
	if err := t.handleDeviceStatusUpdate(); err != nil {
		return err
	}

	// 2. 查询设备信息
	var device model.Device
	db.GetPixmoving().Where("imsi = ?", t.term.Prop.IMSI).First(&device)

	// 3. 杭州项目特殊处理
	t.handleHangzhouProject(device)

	// 4. 任务状态处理
	pix_log.TcpInfoWithIMSI(t.term.Prop.IMSI, "🔍 处理设备任务状态，TaskID=%d, Step=%d", t.heartbeatDevice.LatestTask.TaskID, t.heartbeatDevice.LatestTask.Step)
	if t.heartbeatDevice.LatestTask.TaskID == 0 {
		// 🔄 移除对task/task_child表的状态更新，避免与ccapiex冲突
		// 专注于执行实例状态管理
		t.updateExecutionReportOnNoTask()

		pix_log.TcpInfoWithIMSI(t.term.Prop.IMSI, "🔄 设备无任务，已终止所有执行中的实例")
	} else {
		var child model.TaskChild
		db.GetPixmoving().Where("id", t.heartbeatDevice.LatestTask.TaskID).Take(&child)

		status := child.Status
		step := t.heartbeatDevice.LatestTask.Step
		isNow := 1

		// 根据步骤更新状态
		switch step {
		case 1:
			status = 2
			// 🆕 更新执行实例 - 任务开始
			t.updateExecutionReportOnStart()
		case 2:
			status = 3
			isNow = 0
			// 任务完成处理
			t.handleTaskCompletion(&child, device)
			// 🆕 更新执行实例 - 任务完成
			t.updateExecutionReportOnComplete()
		case 3:
			status = 5
			isNow = 0
			// 🆕 更新执行实例 - 任务取消
			t.updateExecutionReportOnCancel()
		case 6:
			status = 4
			// 🆕 更新执行实例 - 任务暂停
			t.updateExecutionReportOnPause()
		case 8:
			status = 6
			isNow = 0
			// 🆕 更新执行实例 - 任务终止
			t.updateExecutionReportOnTerminate()
		case 9:
			status = 7
			isNow = 0
			// 🆕 更新执行实例 - 任务失败
			t.updateExecutionReportOnFail()
		}

		// 更新任务状态
		db.GetPixmoving().Model(&child).Where("id = ?", child.Id).Updates(map[string]interface{}{
			"status": status,
			"is_now": isNow,
		})

		db.GetPixmoving().Model(&model.Task{}).Where("id", child.ParentId).Update("status", status)
	}

	// 6. 查询用户信息
	var user model.User
	db.GetPixmoving().Where("id = ?", device.Uid).First(&user)

	// 7. 处理告警
	alarmProcessor := NewAlarmProcessor(t, &device, &user)
	alarmProcessor.ProcessAlarms()

	// 8. 数据库记录心跳
	if t.heartbeatDevice.Event == 0 {
		// 创建记录
		record, err := t.ToHeartbeatRecord(lan1Status, lan2Status)
		if err != nil {
			fmt.Printf("生成记录数据错误%v", err)
			pix_log.ErrorWithIMSI(t.term.Prop.IMSI, "生成记录数据错误: %v", err)
			return err
		}

		// 方案1：使用标准库 encoding/json
		recordJSON, err := json.Marshal(record)
		if err != nil {
			return err
		}

		// 调试打印（美化输出）
		var prettyJSON bytes.Buffer
		err = json.Indent(&prettyJSON, recordJSON, "", "    ")
		if err != nil {
			return err
		}

		// pix_log.Info("发送mqtt开始：%s", prettyJSON.String())

		token := mqtt.SharedClient.Publish("/pix/heartbeat/record", 0, false, string(recordJSON))
		token.Wait()

		// pix_log.Info("发送mqtt结束：%s", prettyJSON.String())

		// 保存记录
		// if err := db.GetPixmoving().Create(record).Error; err != nil {
		// 	pix_log.Error("add record_signal fail:", err)
		// 	return err
		// }
	} else {
		now := int(time.Now().Unix())
		msg := protocol.EventData[t.heartbeatDevice.Event]

		// 处理紧急消息
		if common.InArrayUint(t.heartbeatDevice.Event, protocol.EmergencyMessage) {
			notification := model.Notification{
				DeviceId:    int(t.term.Prop.ID),
				Type:        int(t.heartbeatDevice.Event),
				Content:     fmt.Sprintf("来自设备%s的通知：%s", t.term.Prop.IMSI, msg),
				Lng:         t.heartbeatDevice.Longitude,
				Lat:         t.heartbeatDevice.Latitude,
				MessageTime: int(t.ts),
				CreatedTime: now,
				CompanyId:   device.CompanyId,
			}
			db.GetPixmoving().Create(&notification)

			notificationUser := model.NotificationUser{
				UserId:         int(t.term.Prop.UID),
				NotificationId: notification.Id,
				Status:         0,
			}
			db.GetPixmoving().Create(&notificationUser)
		} else {
			// 处理普通消息
			message := model.Message{
				DeviceId:    int(t.term.Prop.ID),
				UserId:      int(t.term.Prop.UID),
				Type:        int(t.heartbeatDevice.Event),
				Content:     msg,
				Lng:         t.heartbeatDevice.Longitude,
				Lat:         t.heartbeatDevice.Latitude,
				MessageTime: int(t.ts),
				CreatedTime: now,
				CompanyId:   device.CompanyId,
			}
			db.GetPixmoving().Create(&message)
		}
	}

	// 10. 特定位置操作计数
	if t.heartbeatDevice.Latitude == 26.748894 {
		db.GetPixmoving().Table("device").
			Where("imsi = ?", t.term.Prop.IMSI).
			UpdateColumn("operation_num", gorm.Expr("operation_num + ?", 1))
	}

	// 11. 零售机器人数据处理
	if err := t.handleRetailBot(); err != nil {
		return err
	}

	return nil
}

// handleTaskCompletion 处理任务完成（重构版本）
func (t *Heartbeat) handleTaskCompletion(child *model.TaskChild, device model.Device) {
	pix_log.TcpInfoWithIMSI(t.term.Prop.IMSI, "🎯 处理任务完成：ChildId=%d, ParentId=%d", child.Id, child.ParentId)

	// 🔄 重构：只更新 is_now 字段，不更新 status 字段（保持模板状态）
	db.GetPixmoving().Model(&child).Where("id = ?", child.Id).Updates(map[string]interface{}{
		"is_now": 0, // 🔄 只更新 is_now，移除 status 更新
	})

	// 🔄 重构：不更新主任务状态，保持任务模板化
	// db.GetPixmoving().Model(&model.Task{}).Where("id", child.ParentId).Update("status", 3)

	// 任务完成，如果是物流小车，则发送短信
	if device.Type == 2 {
		var mails []model.WrcMail
		db.GetDBJzy().Where("device = ? AND station = ? AND is_send = 0", device.IMSI, child.Name).Find(&mails)

		for _, mail := range mails {
			client := http.Client{}
			user := map[string]interface{}{
				"phone": mail.ReceiveTel,
				"name":  mail.ReceiveName,
				"code":  mail.Code,
			}
			dataByte, err := json.Marshal(user)
			if err != nil {
				pix_log.Error(err.Error())
				continue
			}

			bodyReader := bytes.NewReader(dataByte)
			request, _ := http.NewRequestWithContext(context.Background(), http.MethodPost, "https://jzy.pixmoving.city/api/sms/aliyun", bodyReader)
			request.Header.Set("Content-Type", "application/json")
			_, err = client.Do(request)
			if err != nil {
				return
			}

			db.GetDBJzy().Model(&model.WrcMail{}).Where("id = ?", mail.Id).Update("is_send", 1)
		}
	}

	// 记录任务日志
	var oldLog model.TaskLog
	db.GetPixmoving().Table("task_log").Where("imsi = ?", device.IMSI).
		Where("task_id = ?", child.ParentId).Where("child_id = ?", child.Id).Last(&oldLog)

	if oldLog.Status != 3 {
		log := model.TaskLog{
			Imsi:        device.IMSI,
			TaskId:      child.ParentId,
			CreatedTime: time.Now().Unix(),
			Info:        "任务完成",
			ChildId:     child.Id,
			OperationId: 0,
			Status:      3,
		}
		db.GetPixmoving().Create(&log)
	}

	// 🔄 重构：自动下发下一个子任务（基于 order 而非 status）
	var next model.TaskChild
	db.GetPixmoving().Where("parent_id", child.ParentId).Where("is_active", 1).
		Where("`order` > ?", child.Order).Order("`order`").Take(&next)

	if next.Id != 0 {
		pix_log.TcpInfoWithIMSI(t.term.Prop.IMSI, "🚀 找到下一个任务：ChildId=%d, Order=%d", next.Id, next.Order)
		t.handleNextTask(&next, child.ParentId)
	} else {
		pix_log.TcpInfoWithIMSI(t.term.Prop.IMSI, "🏁 所有子任务已完成，任务链式执行结束")
	}
}

// handleNextTask 处理下一个任务（重构版本）
func (t *Heartbeat) handleNextTask(next *model.TaskChild, parentId int64) {
	pix_log.TcpInfoWithIMSI(t.term.Prop.IMSI, "🚀 开始处理下一个任务：ChildId=%d, ParentId=%d, Order=%d", next.Id, parentId, next.Order)

	// 1. 更新主任务状态和执行次数（移除状态更新，只保留执行次数）
	var task model.Task
	db.GetPixmoving().Where("id", parentId).Take(&task)
	task.Times += 1 // 🔄 只更新执行次数，移除状态更新

	if task.Id != 0 {
		db.GetPixmoving().Save(&task)
		pix_log.TcpInfoWithIMSI(t.term.Prop.IMSI, "📊 更新主任务执行次数：TaskId=%d, Times=%d", task.Id, task.Times)
	}

	// 2. 更新下一个子任务的 is_now 标识（🔄 移除状态更新）
	next.IsNow = 1
	// 🚫 移除：next.Status = 1  // 不再更新子任务状态
	db.GetPixmoving().Save(next)
	pix_log.TcpInfoWithIMSI(t.term.Prop.IMSI, "📝 更新子任务 is_now 标识：ChildId=%d", next.Id)

	// 🆕 3. 创建执行实例记录
	executionReport := t.createExecutionReportForNextTask(next, &task)
	if executionReport == nil {
		pix_log.Error("❌ 创建执行实例失败，终止任务下发")
		return
	}

	// 🆕 4. 构建动作列表
	actionList := t.buildActionList(next, parentId)

	// 🆕 5. 发送任务命令（🔄 使用执行实例ID）
	CmdData, _ := gjson.Encode(g.Map{
		"cmd":        protocol.Task,
		"taskId":     executionReport.Id, // 🔄 使用执行实例ID而非子任务ID
		"routeId":    task.MapId,
		"name":       task.Name,
		"desp":       task.Desp,
		"actionList": actionList,
	})

	cmd := common.MakeCmd(protocol.Task, 0, CmdData)
	if err := common.SendToDevice(task.Imsi, cmd); err != nil {
		pix_log.Error("❌ 发送命令到终端失败：" + err.Error())
		return
	}

	pix_log.TcpInfoWithIMSI(t.term.Prop.IMSI, "✅ 下发了第%d个任务，执行实例ID为%d，站点内容为%s", next.Order, executionReport.Id, next.Content)

	// 🆕 6. 更新设备关联的任务ID（现在是执行实例ID）
	t.updateDeviceTaskId(executionReport.Id)

	// 🆕 7. 记录任务日志
	t.recordTaskLog(next, task.Imsi)

	// 8. 处理延迟自动驾驶（保持原有逻辑）
	var condition struct {
		Type int `json:"type"`
		Time int `json:"time"`
	}

	if err := json.Unmarshal([]byte(next.Condition), &condition); err != nil {
		pix_log.Error("json解析失败: " + err.Error())
		return
	}

	if condition.Type == 2 {
		// 🔄 使用执行实例ID而非子任务ID
		go t.handleDelayedAutoDrive(task.Imsi, next.ParentId, int(executionReport.Id), condition.Time)
	}

	pix_log.TcpInfoWithIMSI(t.term.Prop.IMSI, "🎉 任务链式执行完成：ExecutionId=%d, ChildId=%d", executionReport.Id, next.Id)
}

// handleDelayedAutoDrive 处理延迟自动驾驶
func (t *Heartbeat) handleDelayedAutoDrive(imsi string, parentId int64, childId, delayTime int) {
	time.Sleep(time.Duration(delayTime) * time.Second)

	cmdData2, _ := gjson.Encode(g.Map{
		"cmd": protocol.StartAuto,
	})
	cmd := common.MakeCmd(protocol.StartAuto, 0, cmdData2)

	if err := common.SendToDevice(imsi, cmd); err != nil {
		pix_log.Error("发送命令到终端失败：" + err.Error())
		return
	}

	pix_log.TcpInfoWithIMSI(imsi, "type=2为延迟下发，下发了开始自动驾驶任务")

	log := model.TaskLog{
		Imsi:        imsi,
		TaskId:      parentId,
		CreatedTime: time.Now().Unix(),
		Info:        "执行任务",
		ChildId:     childId,
		OperationId: 0,
		Status:      2,
	}
	db.GetPixmoving().Create(&log)
}

// handleRetailBot 处理零售机器人数据
func (t *Heartbeat) handleRetailBot() error {
	var machine model.Machine
	if err := db.GetCoffee().Table("machine").Where("imsi = ?", t.term.Prop.IMSI).First(&machine).Error; err != nil {
		if err != gorm.ErrRecordNotFound {
			return err
		}
	}

	// 更新机器信息
	machine.Imsi = t.term.Prop.IMSI
	machine.BillNo = t.heartbeatDevice.RetailBot.BillNo
	machine.ProductId = t.heartbeatDevice.RetailBot.ProductID
	machine.TaskId = t.heartbeatDevice.RetailBot.TaskID
	machine.DeviceStatus = t.heartbeatDevice.RetailBot.DeviceStatus

	// 设置当前动作
	machine.Action = 0
	if len(t.heartbeatDevice.RetailBot.ActionList) > t.heartbeatDevice.RetailBot.ActionIdx && t.heartbeatDevice.RetailBot.ActionIdx >= 0 {
		machine.Action = t.heartbeatDevice.RetailBot.ActionList[t.heartbeatDevice.RetailBot.ActionIdx]
	}

	// 计算动作列表长度
	actionListLen := 0
	if len(t.heartbeatDevice.RetailBot.ActionList) > 0 {
		actionListLen = t.heartbeatDevice.RetailBot.ActionList[0]
	}

	// 更新其他字段
	machine.TotalTime = t.heartbeatDevice.RetailBot.TotalTime
	machine.RTime = t.heartbeatDevice.RetailBot.RTime
	machine.ActionTime = t.heartbeatDevice.RetailBot.ActionTime
	machine.ActionRTime = t.heartbeatDevice.RetailBot.ActionRTime

	errCode, _ := json.Marshal(t.heartbeatDevice.RetailBot.ErrCode)
	machine.ErrCode = string(errCode)
	errMsg, _ := json.Marshal(t.heartbeatDevice.RetailBot.ErrMsg)
	machine.ErrMsg = string(errMsg)

	machine.CupHolders = t.heartbeatDevice.RetailBot.CupHolders
	machine.Liquid = t.heartbeatDevice.RetailBot.Liquid
	machine.Solid = t.heartbeatDevice.RetailBot.Solid
	machine.LiquidCup = t.heartbeatDevice.RetailBot.LiquidCup
	machine.SolidCup = t.heartbeatDevice.RetailBot.SolidCup
	machine.Ports = t.heartbeatDevice.RetailBot.Ports
	machine.ArmPosX = t.heartbeatDevice.RetailBot.ArmPosX
	machine.OtherStatus1 = t.heartbeatDevice.RetailBot.OtherStatus1
	machine.OtherStatus2 = t.heartbeatDevice.RetailBot.OtherStatus2
	machine.CarP = t.heartbeatDevice.RetailBot.CarP
	machine.CarT = t.heartbeatDevice.RetailBot.CarT
	machine.CarStatus = t.heartbeatDevice.RetailBot.CarStatus
	machine.InvV = t.heartbeatDevice.RetailBot.InvV
	machine.InvF = t.heartbeatDevice.RetailBot.InvF
	machine.InvC = t.heartbeatDevice.RetailBot.InvC
	machine.InvT = t.heartbeatDevice.RetailBot.InvT
	machine.Time = t.heartbeatDevice.RetailBot.Time
	machine.RunTime = t.heartbeatDevice.RetailBot.RunTime
	machine.BootCnt = t.heartbeatDevice.RetailBot.BootCnt
	machine.UpdatedTime = time.Now().Unix()
	machine.Uid = 1
	machine.ActionListLen = actionListLen
	machine.ActionIndex = t.heartbeatDevice.RetailBot.ActionIdx

	// 保存或创建记录
	if machine.Id != 0 {
		if err := db.GetCoffee().Save(&machine).Error; err != nil {
			return err
		}
	} else {
		machine.CreatedTime = time.Now().Unix()
		if err := db.GetCoffee().Create(&machine).Error; err != nil {
			return err
		}
	}

	// 更新订单进度
	if len(t.heartbeatDevice.RetailBot.ActionList) > 0 && t.heartbeatDevice.RetailBot.ActionList[0] != 0 {
		progress := strconv.FormatFloat(float64(t.heartbeatDevice.RetailBot.ActionIdx)/float64(t.heartbeatDevice.RetailBot.ActionList[0])*100, 'f', 2, 64)
		if err := db.GetCoffee().Table("order").
			Where("order_no = ?", t.heartbeatDevice.RetailBot.BillNo).
			Where("`order` = ?", t.heartbeatDevice.RetailBot.TaskID).
			UpdateColumn("progress", progress).Error; err != nil {
			return err
		}
	}

	return nil
}

// boolToInt 将布尔值转换为整数
func boolToInt(b bool) int {
	if b {
		return 1
	}
	return 0
}

// handleHangzhouProject 处理杭州项目特殊逻辑
func (t *Heartbeat) handleHangzhouProject(device model.Device) {
	// 如果不是杭州项目设备,直接返回
	if device.Uid != 223 {
		return
	}

	// 检查是否需要报警
	code, message := t.getAlarmInfo()

	if code == "" {
		t.reportVehicleStatus(device)
	} else {
		t.reportVehicleAlarm(device, code, message)
	}
}

// getAlarmInfo 获取告警信息
func (t *Heartbeat) getAlarmInfo() (code, message string) {
	// 检查故障告警
	if len(t.heartbeatDevice.Warning) > 0 && t.heartbeatDevice.Warning[0] != 0 {
		return "0X0001", "设备故障，故障码为：" + fmt.Sprintf("%d", t.heartbeatDevice.Warning[0])
	}

	// 检查电量告警
	if t.heartbeatDevice.PowerLevel < 5 {
		return "0X0004", "电量低于5%"
	}

	return "", ""
}

// reportVehicleStatus 上报车辆状态
func (t *Heartbeat) reportVehicleStatus(device model.Device) {
	url := "https://api.thousandsim.com/open/api?method=qiancen.rop.vehicle.status.report"

	mode := 0
	if t.heartbeatDevice.Mode != 3 {
		mode = 1
	}

	status := "running"
	if t.heartbeatDevice.LatestTask.Step == 2 {
		status = "done"
	}

	body := map[string]interface{}{
		"vehicleId": fmt.Sprintf("%d", device.Id),
		"location": map[string]interface{}{
			"lon": t.heartbeatDevice.Longitude,
			"lat": t.heartbeatDevice.Latitude,
		},
		"osVer":       device.SoftwareVer,
		"mapVer":      fmt.Sprintf("%d", device.MapId),
		"battery":     t.heartbeatDevice.PowerLevel,
		"leftMileage": t.heartbeatDevice.RemainMileage,
		"speed":       t.heartbeatDevice.Speed,
		"driveMode":   mode,
		"taskId":      t.heartbeatDevice.LatestTask.TaskID,
		"taskStatus":  status,
	}

	t.PostData(body, url)
}

// reportVehicleAlarm 上报车辆告警
func (t *Heartbeat) reportVehicleAlarm(device model.Device, code, message string) {
	url := "https://api.thousandsim.com/open/api?method=qiancen.rop.vehicle.alarm.report"

	body := map[string]interface{}{
		"vehicleId": fmt.Sprintf("%d", device.Id),
		"location": map[string]interface{}{
			"lon": t.heartbeatDevice.Longitude,
			"lat": t.heartbeatDevice.Latitude,
		},
		"code":    code,
		"message": message,
	}

	t.PostData(body, url)
}

// 🆕 任务执行报表更新相关方法 (ccserver独立实现)

// updateExecutionReportOnNoTask 无任务时终止所有执行中的实例
func (t *Heartbeat) updateExecutionReportOnNoTask() {
	// 构建终止原因
	terminationReason := fmt.Sprintf("设备心跳报告无任务状态(TaskID=0)，系统自动终止执行中的实例。终止时间: %s, 设备IP: %s",
		time.Now().Format("2006-01-02 15:04:05"), t.term.Prop.IP)

	// 直接通过数据库更新，不依赖ccapiex代码
	result := db.GetPixmoving().Table("task_execution_report").
		Where("imsi = ? AND status IN (?)", t.term.Prop.IMSI, []int{1, 2, 4}). // 1=将要执行, 2=正在执行, 4=已暂停
		Updates(map[string]interface{}{
			"status":             6, // TaskStatusTerminated = 6 (已终止)
			"updated_time":       time.Now().Unix(),
			"end_time":           time.Now(),
			"termination_reason": terminationReason,
		})

	// 记录详细日志
	if result.Error != nil {
		pix_log.ErrorWithIMSI(t.term.Prop.IMSI, "更新无任务终止状态失败: %v", result.Error)
	} else if result.RowsAffected > 0 {
		pix_log.InfoWithIMSI(t.term.Prop.IMSI, "✅ 无任务状态终止了%d个执行实例，原因: %s", result.RowsAffected, terminationReason)
	}
}

// updateExecutionReportOnStart 任务开始时更新执行实例
func (t *Heartbeat) updateExecutionReportOnStart() {
	executionId := t.getExecutionIdFromHeartbeat()
	if executionId == 0 {
		return
	}

	// 🆕 添加状态检查，避免重复更新
	var currentStatus int
	err := db.GetPixmoving().Table("task_execution_report").
		Where("id = ?", executionId).
		Select("status").
		Scan(&currentStatus).Error

	if err != nil {
		pix_log.ErrorWithIMSI(t.term.Prop.IMSI, "❌ 查询执行实例 %d 状态失败: %v", executionId, err)
		return
	}

	// 如果已经是"正在执行"状态，跳过更新
	if currentStatus == 2 { // TaskStatusExecuting = 2 (正在执行)
		pix_log.TcpInfoWithIMSI(t.term.Prop.IMSI, "⏭️ 执行实例 %d 已是正在执行状态(status=2)，跳过重复更新", executionId)
		return
	}

	now := time.Now()
	updates := map[string]interface{}{
		"departure_time": now,
		"status":         2, // TaskStatusExecuting = 2 (正在执行)
		"updated_time":   now.Unix(),
	}

	// 🆕 从心跳数据中提取计划信息（实现"只增不减"策略）
	// 查询当前数据库中的计划值
	var currentPlannedDuration, currentPlannedDistance int
	var currentValues struct {
		PlannedDurationMin int `json:"planned_duration_min"`
		PlannedDistanceM   int `json:"planned_distance_m"`
	}

	err = db.GetPixmoving().Table("task_execution_report").
		Where("id = ?", executionId).
		Select("planned_duration_min, planned_distance_m").
		Scan(&currentValues).Error

	if err == nil {
		currentPlannedDuration = currentValues.PlannedDurationMin
		currentPlannedDistance = currentValues.PlannedDistanceM
	}

	// planned_duration_min: 只有当新值大于当前值时才更新
	if t.heartbeatDevice.LatestTask.LeftTime > 0 {
		if currentPlannedDuration == 0 || t.heartbeatDevice.LatestTask.LeftTime > currentPlannedDuration {
			updates["planned_duration_min"] = t.heartbeatDevice.LatestTask.LeftTime
			pix_log.TcpInfoWithIMSI(t.term.Prop.IMSI, "🔄 更新planned_duration_min: %d → %d",
				currentPlannedDuration, t.heartbeatDevice.LatestTask.LeftTime)
		} else {
			pix_log.TcpInfoWithIMSI(t.term.Prop.IMSI, "⏭️ planned_duration_min保持不变: 新值%d ≤ 当前值%d",
				t.heartbeatDevice.LatestTask.LeftTime, currentPlannedDuration)
		}
	}

	// planned_distance_m: 只有当新值大于当前值时才更新
	if t.heartbeatDevice.LatestTask.TaskDistance > 0 {
		if currentPlannedDistance == 0 || t.heartbeatDevice.LatestTask.TaskDistance > currentPlannedDistance {
			updates["planned_distance_m"] = t.heartbeatDevice.LatestTask.TaskDistance
			pix_log.TcpInfoWithIMSI(t.term.Prop.IMSI, "🔄 更新planned_distance_m: %d → %d",
				currentPlannedDistance, t.heartbeatDevice.LatestTask.TaskDistance)
		} else {
			pix_log.TcpInfoWithIMSI(t.term.Prop.IMSI, "⏭️ planned_distance_m保持不变: 新值%d ≤ 当前值%d",
				t.heartbeatDevice.LatestTask.TaskDistance, currentPlannedDistance)
		}
	}
	if t.heartbeatDevice.LatestTask.CurrentStation.Name != "" {
		updates["start_location"] = t.heartbeatDevice.LatestTask.CurrentStation.Name
	}
	if t.heartbeatDevice.LatestTask.TargetStation.Name != "" {
		updates["end_location"] = t.heartbeatDevice.LatestTask.TargetStation.Name
	}

	// 处理里程数据
	if t.heartbeatDevice.TotalMileage >= 0 {
		updates["start_mileage_m"] = int(t.heartbeatDevice.TotalMileage * 1000) // 转换为米
	}

	// 🆕 处理CM数据
	if t.heartbeatDevice.CM >= 0 {
		updates["start_cm"] = int(t.heartbeatDevice.CM) // 开始CM值
	}

	// 🆕 添加站点ID信息
	if t.heartbeatDevice.LatestTask.CurrentStation.ID > 0 {
		updates["start_station_id"] = t.heartbeatDevice.LatestTask.CurrentStation.ID
	}
	if t.heartbeatDevice.LatestTask.TargetStation.ID > 0 {
		updates["end_station_id"] = t.heartbeatDevice.LatestTask.TargetStation.ID
	}

	// 🆕 添加任务名称和描述信息
	if t.heartbeatDevice.LatestTask.Name != "" {
		updates["task_name"] = t.heartbeatDevice.LatestTask.Name
	}
	if t.heartbeatDevice.LatestTask.Desp != "" {
		updates["task_description"] = t.heartbeatDevice.LatestTask.Desp
	}

	// 🆕 添加路线ID信息
	if t.heartbeatDevice.LatestTask.RouteID > 0 {
		updates["route_id"] = t.heartbeatDevice.LatestTask.RouteID
	}

	pix_log.TcpInfoWithIMSI(t.term.Prop.IMSI, "🚀 首次更新执行实例 %d 为开始状态(status: %d → 2)", executionId, currentStatus)
	db.GetPixmoving().Table("task_execution_report").Where("id = ?", executionId).Updates(updates)
}

// updateExecutionReportOnComplete 任务完成时更新执行实例
func (t *Heartbeat) updateExecutionReportOnComplete() {
	executionId := t.getExecutionIdFromHeartbeat()
	if executionId == 0 {
		return
	}

	// 🆕 添加状态检查，避免重复更新
	var currentStatus int
	err := db.GetPixmoving().Table("task_execution_report").
		Where("id = ?", executionId).
		Select("status").
		Scan(&currentStatus).Error

	if err != nil {
		pix_log.ErrorWithIMSI(t.term.Prop.IMSI, "❌ 查询执行实例 %d 状态失败: %v", executionId, err)
		return
	}

	// 检查是否已经是完成状态，避免重复更新
	expectedStatus := t.mapStepToStatus(2) // step=2 对应 status=3 (已完成)
	if currentStatus == expectedStatus {
		pix_log.TcpInfoWithIMSI(t.term.Prop.IMSI, "⏭️ 执行实例 %d 已是完成状态(status=%d)，跳过重复更新", executionId, expectedStatus)
		return
	}

	now := time.Now()
	updates := map[string]interface{}{
		"end_time":     now,
		"arrival_time": now,            // 🆕 添加实际到达时间
		"status":       expectedStatus, // 使用映射后的状态值
		"updated_time": now.Unix(),
	}

	// 🆕 计算实际执行时长（使用服务器记录的时间）
	var departureTime time.Time
	err = db.GetPixmoving().Table("task_execution_report").
		Where("id = ?", executionId).
		Select("departure_time").
		Scan(&departureTime).Error

	if err == nil && !departureTime.IsZero() {
		actualDurationSec := int64(now.Sub(departureTime).Seconds())
		actualDurationMin := int(actualDurationSec / 60)
		if actualDurationMin > 0 {
			updates["actual_duration_min"] = actualDurationMin
		}
	}

	// 处理里程数据
	if t.heartbeatDevice.TotalMileage >= 0 {
		endMileage := int(t.heartbeatDevice.TotalMileage * 1000) // 转换为米
		updates["end_mileage_m"] = endMileage

		// 🆕 计算实际运行里程（需要从数据库获取开始里程）
		var startMileage int
		db.GetPixmoving().Table("task_execution_report").
			Where("id = ?", executionId).
			Select("start_mileage_m").
			Scan(&startMileage)

		if startMileage > 0 {
			actualMileage := endMileage - startMileage
			if actualMileage > 0 {
				updates["actual_mileage_m"] = actualMileage

				// 🆕 计算实际运行速度
				if actualDurationMin, exists := updates["actual_duration_min"]; exists {
					if durationMin, ok := actualDurationMin.(int); ok && durationMin > 0 {
						// 计算速度：(里程米转公里) / (时间分钟转小时) = 公里/小时
						actualSpeedKmh := float64(actualMileage) / 1000.0 / (float64(durationMin) / 60.0)
						// 保留2位小数
						updates["actual_speed_kmh"] = float64(int(actualSpeedKmh*100+0.5)) / 100.0
					}
				}
			}
		}
	}

	// 🆕 处理CM数据
	if t.heartbeatDevice.CM >= 0 {
		updates["end_cm"] = int(t.heartbeatDevice.CM) // 结束CM值
	}

	pix_log.TcpInfoWithIMSI(t.term.Prop.IMSI, "✅ 首次更新执行实例 %d 为完成状态(status: %d → %d)", executionId, currentStatus, expectedStatus)
	db.GetPixmoving().Table("task_execution_report").Where("id = ?", executionId).Updates(updates)
}

// updateExecutionReportOnCancel 任务取消时更新执行实例
func (t *Heartbeat) updateExecutionReportOnCancel() {
	executionId := t.getExecutionIdFromHeartbeat()
	if executionId == 0 {
		return
	}

	// 🆕 添加状态检查，避免重复更新
	var currentStatus int
	err := db.GetPixmoving().Table("task_execution_report").
		Where("id = ?", executionId).
		Select("status").
		Scan(&currentStatus).Error

	if err != nil {
		pix_log.ErrorWithIMSI(t.term.Prop.IMSI, "❌ 查询执行实例 %d 状态失败: %v", executionId, err)
		return
	}

	// 检查是否已经是取消状态，避免重复更新
	expectedStatus := t.mapStepToStatus(3) // step=3 对应 status=5 (已取消)
	if currentStatus == expectedStatus {
		pix_log.TcpInfoWithIMSI(t.term.Prop.IMSI, "⏭️ 执行实例 %d 已是取消状态(status=%d)，跳过重复更新", executionId, expectedStatus)
		return
	}

	now := time.Now()
	terminationReason := fmt.Sprintf("设备心跳报告任务取消状态(Step=3)，TaskID=%d。取消时间: %s, 设备IP: %s",
		t.heartbeatDevice.LatestTask.TaskID, now.Format("2006-01-02 15:04:05"), t.term.Prop.IP)

	updates := map[string]interface{}{
		"status":             expectedStatus, // 使用映射后的状态值
		"end_time":           now,
		"updated_time":       now.Unix(),
		"termination_reason": terminationReason,
	}

	// 🆕 计算实际执行时长（使用服务器记录的时间）
	var departureTime time.Time
	err = db.GetPixmoving().Table("task_execution_report").
		Where("id = ?", executionId).
		Select("departure_time").
		Scan(&departureTime).Error

	if err == nil && !departureTime.IsZero() {
		actualDurationSec := int64(now.Sub(departureTime).Seconds())
		actualDurationMin := int(actualDurationSec / 60)
		if actualDurationMin > 0 {
			updates["actual_duration_min"] = actualDurationMin
		}
	}

	// 🆕 处理里程数据
	if t.heartbeatDevice.TotalMileage >= 0 {
		endMileage := int(t.heartbeatDevice.TotalMileage * 1000) // 转换为米
		updates["end_mileage_m"] = endMileage

		// 计算实际运行里程
		var startMileage int
		db.GetPixmoving().Table("task_execution_report").
			Where("id = ?", executionId).
			Select("start_mileage_m").
			Scan(&startMileage)

		if startMileage > 0 {
			actualMileage := endMileage - startMileage
			if actualMileage > 0 {
				updates["actual_mileage_m"] = actualMileage

				// 🆕 计算实际运行速度
				if actualDurationMin, exists := updates["actual_duration_min"]; exists {
					if durationMin, ok := actualDurationMin.(int); ok && durationMin > 0 {
						// 计算速度：(里程米转公里) / (时间分钟转小时) = 公里/小时
						actualSpeedKmh := float64(actualMileage) / 1000.0 / (float64(durationMin) / 60.0)
						// 保留2位小数
						updates["actual_speed_kmh"] = float64(int(actualSpeedKmh*100+0.5)) / 100.0
					}
				}
			}
		}
	}

	// 🆕 处理CM数据
	if t.heartbeatDevice.CM >= 0 {
		updates["end_cm"] = int(t.heartbeatDevice.CM) // 结束CM值
	}

	pix_log.TcpInfoWithIMSI(t.term.Prop.IMSI, "🚫 首次更新执行实例 %d 为取消状态(status: %d → %d)", executionId, currentStatus, expectedStatus)
	db.GetPixmoving().Table("task_execution_report").Where("id = ?", executionId).Updates(updates)
}

// updateExecutionReportOnPause 任务暂停时更新执行实例
func (t *Heartbeat) updateExecutionReportOnPause() {
	executionId := t.getExecutionIdFromHeartbeat()
	if executionId == 0 {
		return
	}

	// 🆕 添加状态检查，避免重复更新
	var currentStatus int
	err := db.GetPixmoving().Table("task_execution_report").
		Where("id = ?", executionId).
		Select("status").
		Scan(&currentStatus).Error

	if err != nil {
		pix_log.ErrorWithIMSI(t.term.Prop.IMSI, "❌ 查询执行实例 %d 状态失败: %v", executionId, err)
		return
	}

	// 检查是否已经是暂停状态，避免重复更新
	expectedStatus := t.mapStepToStatus(6) // step=6 对应 status=4 (已暂停)
	if currentStatus == expectedStatus {
		pix_log.TcpInfoWithIMSI(t.term.Prop.IMSI, "⏭️ 执行实例 %d 已是暂停状态(status=%d)，跳过重复更新", executionId, expectedStatus)
		return
	}

	updates := map[string]interface{}{
		"status":       expectedStatus, // 使用映射后的状态值
		"updated_time": time.Now().Unix(),
	}

	pix_log.TcpInfoWithIMSI(t.term.Prop.IMSI, "⏸️ 首次更新执行实例 %d 为暂停状态(status: %d → %d)", executionId, currentStatus, expectedStatus)
	db.GetPixmoving().Table("task_execution_report").Where("id = ?", executionId).Updates(updates)
}

// updateExecutionReportOnTerminate 任务终止时更新执行实例
func (t *Heartbeat) updateExecutionReportOnTerminate() {
	executionId := t.getExecutionIdFromHeartbeat()
	if executionId == 0 {
		return
	}

	// 🆕 添加状态检查，避免重复更新
	var currentStatus int
	err := db.GetPixmoving().Table("task_execution_report").
		Where("id = ?", executionId).
		Select("status").
		Scan(&currentStatus).Error

	if err != nil {
		pix_log.ErrorWithIMSI(t.term.Prop.IMSI, "❌ 查询执行实例 %d 状态失败: %v", executionId, err)
		return
	}

	// 检查是否已经是终止状态，避免重复更新
	expectedStatus := t.mapStepToStatus(8) // step=8 对应 status=6 (已终止)
	if currentStatus == expectedStatus {
		pix_log.TcpInfoWithIMSI(t.term.Prop.IMSI, "⏭️ 执行实例 %d 已是终止状态(status=%d)，跳过重复更新", executionId, expectedStatus)
		return
	}

	now := time.Now()
	terminationReason := fmt.Sprintf("设备心跳报告任务终止状态(Step=8)，TaskID=%d。终止时间: %s, 设备IP: %s",
		t.heartbeatDevice.LatestTask.TaskID, now.Format("2006-01-02 15:04:05"), t.term.Prop.IP)

	updates := map[string]interface{}{
		"status":             expectedStatus, // 使用映射后的状态值
		"end_time":           now,
		"updated_time":       now.Unix(),
		"termination_reason": terminationReason,
	}

	// 🆕 计算实际执行时长（使用服务器记录的时间）
	var departureTime time.Time
	err = db.GetPixmoving().Table("task_execution_report").
		Where("id = ?", executionId).
		Select("departure_time").
		Scan(&departureTime).Error

	if err == nil && !departureTime.IsZero() {
		actualDurationSec := int64(now.Sub(departureTime).Seconds())
		actualDurationMin := int(actualDurationSec / 60)
		if actualDurationMin > 0 {
			updates["actual_duration_min"] = actualDurationMin
		}
	}

	// 🆕 处理里程数据
	if t.heartbeatDevice.TotalMileage >= 0 {
		endMileage := int(t.heartbeatDevice.TotalMileage * 1000) // 转换为米
		updates["end_mileage_m"] = endMileage

		// 计算实际运行里程
		var startMileage int
		db.GetPixmoving().Table("task_execution_report").
			Where("id = ?", executionId).
			Select("start_mileage_m").
			Scan(&startMileage)

		if startMileage > 0 {
			actualMileage := endMileage - startMileage
			if actualMileage > 0 {
				updates["actual_mileage_m"] = actualMileage

				// 🆕 计算实际运行速度
				if actualDurationMin, exists := updates["actual_duration_min"]; exists {
					if durationMin, ok := actualDurationMin.(int); ok && durationMin > 0 {
						// 计算速度：(里程米转公里) / (时间分钟转小时) = 公里/小时
						actualSpeedKmh := float64(actualMileage) / 1000.0 / (float64(durationMin) / 60.0)
						// 保留2位小数
						updates["actual_speed_kmh"] = float64(int(actualSpeedKmh*100+0.5)) / 100.0
					}
				}
			}
		}
	}

	// 🆕 处理CM数据
	if t.heartbeatDevice.CM >= 0 {
		updates["end_cm"] = int(t.heartbeatDevice.CM) // 结束CM值
	}

	pix_log.TcpInfoWithIMSI(t.term.Prop.IMSI, "🛑 首次更新执行实例 %d 为终止状态(status: %d → %d)", executionId, currentStatus, expectedStatus)
	db.GetPixmoving().Table("task_execution_report").Where("id = ?", executionId).Updates(updates)
}

// updateExecutionReportOnFail 任务失败时更新执行实例
func (t *Heartbeat) updateExecutionReportOnFail() {
	executionId := t.getExecutionIdFromHeartbeat()
	if executionId == 0 {
		return
	}

	// 🆕 添加状态检查，避免重复更新
	var currentStatus int
	err := db.GetPixmoving().Table("task_execution_report").
		Where("id = ?", executionId).
		Select("status").
		Scan(&currentStatus).Error

	if err != nil {
		pix_log.ErrorWithIMSI(t.term.Prop.IMSI, "❌ 查询执行实例 %d 状态失败: %v", executionId, err)
		return
	}

	// 检查是否已经是失败状态，避免重复更新
	expectedStatus := t.mapStepToStatus(9) // step=9 对应 status=7 (无法完成)
	if currentStatus == expectedStatus {
		pix_log.TcpInfoWithIMSI(t.term.Prop.IMSI, "⏭️ 执行实例 %d 已是失败状态(status=%d)，跳过重复更新", executionId, expectedStatus)
		return
	}

	now := time.Now()
	terminationReason := fmt.Sprintf("设备心跳报告任务失败状态(Step=9)，TaskID=%d。失败时间: %s, 设备IP: %s",
		t.heartbeatDevice.LatestTask.TaskID, now.Format("2006-01-02 15:04:05"), t.term.Prop.IP)

	updates := map[string]interface{}{
		"status":             expectedStatus, // 使用映射后的状态值
		"end_time":           now,
		"updated_time":       now.Unix(),
		"termination_reason": terminationReason,
	}

	// 🆕 计算实际执行时长（使用服务器记录的时间）
	var departureTime time.Time
	err = db.GetPixmoving().Table("task_execution_report").
		Where("id = ?", executionId).
		Select("departure_time").
		Scan(&departureTime).Error

	if err == nil && !departureTime.IsZero() {
		actualDurationSec := int64(now.Sub(departureTime).Seconds())
		actualDurationMin := int(actualDurationSec / 60)
		if actualDurationMin > 0 {
			updates["actual_duration_min"] = actualDurationMin
		}
	}

	// 🆕 处理里程数据
	if t.heartbeatDevice.TotalMileage >= 0 {
		endMileage := int(t.heartbeatDevice.TotalMileage * 1000) // 转换为米
		updates["end_mileage_m"] = endMileage

		// 计算实际运行里程
		var startMileage int
		db.GetPixmoving().Table("task_execution_report").
			Where("id = ?", executionId).
			Select("start_mileage_m").
			Scan(&startMileage)

		if startMileage > 0 {
			actualMileage := endMileage - startMileage
			if actualMileage > 0 {
				updates["actual_mileage_m"] = actualMileage

				// 🆕 计算实际运行速度
				if actualDurationMin, exists := updates["actual_duration_min"]; exists {
					if durationMin, ok := actualDurationMin.(int); ok && durationMin > 0 {
						// 计算速度：(里程米转公里) / (时间分钟转小时) = 公里/小时
						actualSpeedKmh := float64(actualMileage) / 1000.0 / (float64(durationMin) / 60.0)
						// 保留2位小数
						updates["actual_speed_kmh"] = float64(int(actualSpeedKmh*100+0.5)) / 100.0
					}
				}
			}
		}
	}

	// 🆕 处理CM数据
	if t.heartbeatDevice.CM >= 0 {
		updates["end_cm"] = int(t.heartbeatDevice.CM) // 结束CM值
	}

	pix_log.TcpInfoWithIMSI(t.term.Prop.IMSI, "❌ 首次更新执行实例 %d 为失败状态(status: %d → %d)", executionId, currentStatus, expectedStatus)
	db.GetPixmoving().Table("task_execution_report").Where("id = ?", executionId).Updates(updates)
}

// getExecutionIdFromHeartbeat 从心跳数据中获取执行实例ID
func (t *Heartbeat) getExecutionIdFromHeartbeat() int64 {
	// 🔄 简化逻辑：taskId 直接是执行实例ID
	executionId := t.heartbeatDevice.LatestTask.TaskID

	if executionId > 0 {
		// 🆕 验证是否为有效的执行实例ID
		if t.isValidExecutionId(executionId) {
			pix_log.TcpInfoWithIMSI(t.term.Prop.IMSI, "✅ 获取到执行实例ID: %d", executionId)
			return executionId
		} else {
			pix_log.Warning(fmt.Sprintf("⚠️ 设备 %s 的taskId %d 不是有效的执行实例ID",
				t.term.Prop.IMSI, executionId))
		}
	}

	pix_log.Warning(fmt.Sprintf("❌ 设备 %s 心跳数据中未包含有效的执行实例ID", t.term.Prop.IMSI))
	return 0
}

// isValidExecutionId 验证执行实例ID的有效性
func (t *Heartbeat) isValidExecutionId(executionId int64) bool {
	var count int64
	err := db.GetPixmoving().Table("task_execution_report").
		Where("id = ? AND imsi = ?", executionId, t.term.Prop.IMSI).
		Count(&count).Error

	return err == nil && count > 0
}

// isTaskStatusChanged 检测任务状态是否发生变化
func (t *Heartbeat) isTaskStatusChanged() bool {
	// 🆕 基于数据库查询检测关键任务状态变化，这些状态变化需要立即处理，不受时间间隔限制
	currentStep := t.heartbeatDevice.LatestTask.Step
	currentTaskId := t.heartbeatDevice.LatestTask.TaskID

	// 只处理有任务的情况
	if currentTaskId == 0 {
		return false
	}

	// 只处理关键状态变化：任务开始(step=1)、任务完成(step=2)、任务暂停(step=6)、任务终止(step=8)、任务失败(step=9)
	if currentStep != 1 && currentStep != 2 && currentStep != 6 && currentStep != 8 && currentStep != 9 {
		return false
	}

	// 获取当前executionId
	executionId := t.getExecutionIdFromHeartbeat()
	if executionId == 0 {
		pix_log.Warning(fmt.Sprintf("⚠️ 设备 %s 无法获取executionId，跳过状态变化检测", t.term.Prop.IMSI))
		return false
	}

	// 查询数据库中当前执行实例的状态
	var dbStatus int
	err := db.GetPixmoving().Table("task_execution_report").
		Where("id = ?", executionId).
		Select("status").
		Scan(&dbStatus).Error

	if err != nil {
		pix_log.Warning(fmt.Sprintf("⚠️ 设备 %s 查询执行实例 %d 状态失败: %v", t.term.Prop.IMSI, executionId, err))
		return false
	}

	// 将step值映射为期望的status
	expectedStatus := t.mapStepToStatus(int(currentStep))

	// 如果数据库状态与期望状态不一致，说明需要更新
	if dbStatus != expectedStatus {
		pix_log.TcpInfoWithIMSI(t.term.Prop.IMSI, "🔄 任务状态变化检测：ExecutionId=%d, Step=%d, 数据库状态=%d → 期望状态=%d",
			executionId, currentStep, dbStatus, expectedStatus)
		return true
	}

	pix_log.TcpInfoWithIMSI(t.term.Prop.IMSI, "✅ 任务状态一致：ExecutionId=%d, Step=%d, 状态=%d",
		executionId, currentStep, dbStatus)
	return false
}

// mapStepToStatus 将step值映射为status数字状态
func (t *Heartbeat) mapStepToStatus(step int) int {
	switch step {
	case 1:
		return 2 // 正在执行
	case 2:
		return 3 // 已完成
	case 3:
		return 5 // 已取消
	case 6:
		return 4 // 已暂停
	case 8:
		return 6 // 已终止
	case 9:
		return 7 // 无法完成
	default:
		return 1 // 将要执行
	}
}

// ==================== 🆕 任务链式执行重构相关函数 ====================

// createExecutionReportForNextTask 为下一个任务创建执行实例
func (t *Heartbeat) createExecutionReportForNextTask(child *model.TaskChild, task *model.Task) *model.TaskExecutionReport {
	// 查询设备信息
	var device model.Device
	db.GetPixmoving().Where("imsi = ?", t.term.Prop.IMSI).First(&device)

	// 创建执行实例记录
	executionReport := &model.TaskExecutionReport{
		TaskId:        int64(task.Id),
		ChildId:       child.Id,
		ExecutionNo:   task.Times,
		Imsi:          task.Imsi,
		RunningTask:   fmt.Sprintf("%d-%d", child.Id, task.Times),
		Status:        1, // 将要执行
		VehiclePlate:  task.Imsi,
		VehicleType:   t.getVehicleType(device.Type),
		TaskType:      child.Type,
		VehicleUsage:  t.getVehicleUsage(device.Type),
		Creator:       "system_auto", // 系统自动下发
		CreatedTime:   time.Now().Unix(),
		ExecutionHash: t.generateExecutionHash(child.Id, task.Times),
	}

	// 解析任务内容，填充地点信息
	if err := t.parseTaskContent(executionReport, child.Content, task.MapId); err != nil {
		pix_log.Error("解析任务内容失败: " + err.Error())
	}

	// 保存到数据库
	if err := db.GetPixmoving().Create(executionReport).Error; err != nil {
		pix_log.Error("创建执行实例失败: " + err.Error())
		return nil
	}

	pix_log.TcpInfoWithIMSI(t.term.Prop.IMSI, "✅ 创建执行实例成功：ID=%d, ChildId=%d, ExecutionNo=%d",
		executionReport.Id, child.Id, task.Times)

	return executionReport
}

// getVehicleType 根据设备类型获取车辆类型
func (t *Heartbeat) getVehicleType(deviceType int) string {
	if vehicleType, exists := model.VehicleTypeMap[deviceType]; exists {
		return vehicleType
	}
	return "未知车型"
}

// getVehicleUsage 根据设备类型获取车辆用途
func (t *Heartbeat) getVehicleUsage(deviceType int) string {
	if vehicleUsage, exists := model.VehicleUsageMap[deviceType]; exists {
		return vehicleUsage
	}
	return "未知用途"
}

// parseTaskContent 解析任务内容并填充地点信息
func (t *Heartbeat) parseTaskContent(report *model.TaskExecutionReport, content string, mapId int) error {
	// 尝试解析JSON内容
	var taskContent map[string]interface{}
	if err := json.Unmarshal([]byte(content), &taskContent); err != nil {
		// 如果不是JSON格式，直接使用原始内容
		report.OriginalTaskContent = content
		return nil
	}

	// 保存原始任务内容
	report.OriginalTaskContent = content

	// 尝试提取起点和终点信息
	if startLocation, exists := taskContent["start_location"]; exists {
		if startStr, ok := startLocation.(string); ok {
			report.StartLocation = startStr
		}
	}

	if endLocation, exists := taskContent["end_location"]; exists {
		if endStr, ok := endLocation.(string); ok {
			report.EndLocation = endStr
		}
	}

	// 如果没有明确的起点终点，使用content作为起点
	if report.StartLocation == "" && report.EndLocation == "" {
		report.StartLocation = content
	}

	return nil
}

// generateExecutionHash 生成执行唯一标识
func (t *Heartbeat) generateExecutionHash(childId int, executionNo int) string {
	return fmt.Sprintf("exec_%d_%d_%d", childId, executionNo, time.Now().Unix())
}

// buildActionList 构建动作列表
func (t *Heartbeat) buildActionList(next *model.TaskChild, parentId int64) []TaskContent {
	actionList := make([]TaskContent, 0)

	// 添加当前任务
	actionList = append(actionList, TaskContent{
		Action:  next.Action,
		Content: next.Content,
		PST:     int(time.Now().Unix()),
		PET:     0,
	})

	// 查找下一个子任务
	var nextChild model.TaskChild
	db.GetPixmoving().Table("task_child").Where("parent_id", parentId).Where("is_active", 1).
		Where("order", next.Order+1).Take(&nextChild)

	if nextChild.Id != 0 {
		actionList = append(actionList, TaskContent{
			Action:  nextChild.Action,
			Content: nextChild.Content,
			PST:     int(time.Now().Unix()),
			PET:     0,
		})
	}

	return actionList
}

// recordTaskLog 记录任务日志
func (t *Heartbeat) recordTaskLog(child *model.TaskChild, imsi string) {
	log := model.TaskLog{
		Imsi:        imsi,
		TaskId:      child.ParentId,
		CreatedTime: time.Now().Unix(),
		Info:        "系统自动下发任务",
		ChildId:     child.Id,
		OperationId: 0,
		Status:      1,
	}
	db.GetPixmoving().Create(&log)
}

// updateDeviceTaskId 更新设备关联的任务ID（现在是执行实例ID）
func (t *Heartbeat) updateDeviceTaskId(executionId int64) {
	db.GetPixmoving().Table("device").Where("imsi", t.term.Prop.IMSI).Update("task_id", executionId)
}

// recordSecurityEvent 记录安全事件
func (t *Heartbeat) recordSecurityEvent(event string, reason string) {
	// 简化日志输出，避免重复
	pix_log.WarningWithIMSI(t.term.Prop.IMSI, "🔒 [SECURITY] %s: IP=%s", event, t.term.Prop.IP)
}

// disconnectUnauthorizedDevice 断开未授权设备连接
func (t *Heartbeat) disconnectUnauthorizedDevice(reason string) {
	t.term.Prop.QuitReason = reason

	// 🆕 增强日志：记录心跳处理中的安全断开
	connectionDuration := time.Since(t.term.Prop.ConnectAt)
	pix_log.WarningWithIMSI(t.term.Prop.IMSI, "🔒 [HEARTBEAT_SECURITY] 心跳处理中断开未授权设备：IP=%s, 原因=%s, 连接时长=%v, Step=%d, TaskId=%d",
		t.term.Prop.IP, reason, connectionDuration, t.heartbeatDevice.LatestTask.Step, t.heartbeatDevice.LatestTask.TaskID)

	// 立即断开心跳处理中的未授权连接（因为不需要发送响应）
	go func() {
		defer func() {
			if r := recover(); r != nil {
				pix_log.ErrorWithIMSI(t.term.Prop.IMSI, "断开连接时发生panic: %v", r)
			}
		}()

		// 标记连接为断开状态
		t.term.IsConnected = false

		select {
		case t.term.Prop.Quit <- true:
			pix_log.InfoWithIMSI(t.term.Prop.IMSI, "✅ [HEARTBEAT_SECURITY] 未授权设备连接已断开：IP=%s, Reason=%s, 立即断开",
				t.term.Prop.IP, reason)
			v.LogTcp().Infof("✅ [SECURITY] 未授权设备连接已断开：IMSI=%s, IP=%s", t.term.Prop.IMSI, t.term.Prop.IP)
		case <-time.After(5 * time.Second):
			// 超时处理：强制标记连接为断开状态
			pix_log.WarningWithIMSI(t.term.Prop.IMSI, "⚠️ [HEARTBEAT_SECURITY] 断开连接信号发送超时：IP=%s, 可能存在死锁", t.term.Prop.IP)
		}
	}()
}
