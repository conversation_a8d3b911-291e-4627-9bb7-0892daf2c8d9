package api

import (
	"ccserver/app/service/tdengine"
	"ccserver/pix_log"
	"strconv"
	"strings"
	"time"

	"github.com/gogf/gf/net/ghttp"
)

// DeviceStatusController 设备状态控制器
type DeviceStatusController struct{}

// APIResponse 统一API响应格式
type APIResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

// DeviceListResponse 设备列表响应
type DeviceListResponse struct {
	Total   int                        `json:"total"`
	Devices []*tdengine.DeviceStatus   `json:"devices"`
	Stats   *DeviceStats               `json:"stats"`
}

// DeviceStats 设备统计信息
type DeviceStats struct {
	TotalDevices   int `json:"total_devices"`
	OnlineDevices  int `json:"online_devices"`
	OfflineDevices int `json:"offline_devices"`
	ErrorDevices   int `json:"error_devices"`
	OnlineRate     int `json:"online_rate"`
	OfflineRate    int `json:"offline_rate"`
}

// NewDeviceStatusController 创建设备状态控制器
func NewDeviceStatusController() *DeviceStatusController {
	return &DeviceStatusController{}
}

// GetDeviceList 获取设备列表
func (c *DeviceStatusController) GetDeviceList(r *ghttp.Request) {
	pix_log.Info("[API] 收到设备列表请求: %s", r.URL.String())

	// 创建TDengine客户端
	client := tdengine.NewTDengineClient()

	// 获取所有设备状态
	devices, err := client.GetAllDevicesStatus()
	if err != nil {
		pix_log.Error("[API] 获取设备状态失败: %v", err)
		r.Response.WriteJsonExit(APIResponse{
			Code:    500,
			Message: "获取设备状态失败: " + err.Error(),
			Data:    nil,
		})
		return
	}

	// 获取查询参数
	imsiSearch := r.GetString("imsi", "")
	statusFilter := r.GetString("status", "")
	startTimeStr := r.GetString("start_time", "")
	endTimeStr := r.GetString("end_time", "")
	page := r.GetInt("page", 1)
	pageSize := r.GetInt("page_size", 20)

	// 过滤设备
	filteredDevices := filterDevices(devices, imsiSearch, statusFilter, startTimeStr, endTimeStr)

	// 计算统计信息
	stats := calculateStats(filteredDevices)

	// 分页处理
	total := len(filteredDevices)
	start := (page - 1) * pageSize
	end := start + pageSize
	if start > total {
		start = total
	}
	if end > total {
		end = total
	}

	pagedDevices := filteredDevices[start:end]

	// 返回响应
	response := DeviceListResponse{
		Total:   total,
		Devices: pagedDevices,
		Stats:   stats,
	}

	pix_log.Info("[API] 返回设备列表: 总数=%d, 当前页=%d, 页大小=%d", total, page, pageSize)

	r.Response.WriteJsonExit(APIResponse{
		Code:    200,
		Message: "success",
		Data:    response,
	})
}

// GetDeviceDetail 获取设备详情
func (c *DeviceStatusController) GetDeviceDetail(r *ghttp.Request) {
	imsi := r.GetString("imsi")
	if imsi == "" {
		r.Response.WriteJsonExit(APIResponse{
			Code:    400,
			Message: "IMSI参数不能为空",
			Data:    nil,
		})
		return
	}

	pix_log.Info("[API] 收到设备详情请求: IMSI=%s", imsi)

	// 创建TDengine客户端
	client := tdengine.NewTDengineClient()

	// 获取设备最新状态
	tableName := "device_heartbeat_" + imsi
	device, err := client.GetLatestDeviceStatus(tableName)
	if err != nil {
		pix_log.Error("[API] 获取设备详情失败: IMSI=%s, 错误=%v", imsi, err)
		r.Response.WriteJsonExit(APIResponse{
			Code:    404,
			Message: "设备不存在或获取失败: " + err.Error(),
			Data:    nil,
		})
		return
	}

	pix_log.Info("[API] 返回设备详情: IMSI=%s", imsi)

	r.Response.WriteJsonExit(APIResponse{
		Code:    200,
		Message: "success",
		Data:    device,
	})
}

// GetDeviceHistory 获取设备历史数据
func (c *DeviceStatusController) GetDeviceHistory(r *ghttp.Request) {
	imsi := r.GetString("imsi")
	if imsi == "" {
		r.Response.WriteJsonExit(APIResponse{
			Code:    400,
			Message: "IMSI参数不能为空",
			Data:    nil,
		})
		return
	}

	// 解析时间参数
	startTimeStr := r.GetString("start_time", "")
	endTimeStr := r.GetString("end_time", "")
	limit := r.GetInt("limit", 100)

	var startTime, endTime time.Time
	var err error

	if startTimeStr != "" {
		startTime, err = time.Parse("2006-01-02T15:04:05", startTimeStr)
		if err != nil {
			r.Response.WriteJsonExit(APIResponse{
				Code:    400,
				Message: "开始时间格式错误",
				Data:    nil,
			})
			return
		}
	} else {
		startTime = time.Now().AddDate(0, 0, -1) // 默认查询最近24小时
	}

	if endTimeStr != "" {
		endTime, err = time.Parse("2006-01-02T15:04:05", endTimeStr)
		if err != nil {
			r.Response.WriteJsonExit(APIResponse{
				Code:    400,
				Message: "结束时间格式错误",
				Data:    nil,
			})
			return
		}
	} else {
		endTime = time.Now()
	}

	pix_log.Info("[API] 收到设备历史请求: IMSI=%s, 时间范围=%s到%s, 限制=%d", 
		imsi, startTime.Format("2006-01-02 15:04:05"), endTime.Format("2006-01-02 15:04:05"), limit)

	// 创建TDengine客户端
	client := tdengine.NewTDengineClient()

	// 获取设备历史数据
	history, err := client.GetDeviceHistory(imsi, startTime, endTime, limit)
	if err != nil {
		pix_log.Error("[API] 获取设备历史失败: IMSI=%s, 错误=%v", imsi, err)
		r.Response.WriteJsonExit(APIResponse{
			Code:    500,
			Message: "获取设备历史失败: " + err.Error(),
			Data:    nil,
		})
		return
	}

	pix_log.Info("[API] 返回设备历史: IMSI=%s, 记录数=%d", imsi, len(history))

	r.Response.WriteJsonExit(APIResponse{
		Code:    200,
		Message: "success",
		Data:    history,
	})
}

// filterDevices 过滤设备列表
func filterDevices(devices []*tdengine.DeviceStatus, imsiSearch, statusFilter, startTimeStr, endTimeStr string) []*tdengine.DeviceStatus {
	var filtered []*tdengine.DeviceStatus

	for _, device := range devices {
		// IMSI搜索过滤
		if imsiSearch != "" && !strings.Contains(strings.ToLower(device.IMSI), strings.ToLower(imsiSearch)) {
			continue
		}

		// 状态过滤
		if statusFilter != "" {
			if status, err := strconv.Atoi(statusFilter); err == nil {
				if device.CurrentStatus != status {
					continue
				}
			}
		}

		// 时间范围过滤
		if startTimeStr != "" {
			if startTime, err := time.Parse("2006-01-02T15:04:05", startTimeStr); err == nil {
				if device.LastHeartbeatTime.Before(startTime) {
					continue
				}
			}
		}

		if endTimeStr != "" {
			if endTime, err := time.Parse("2006-01-02T15:04:05", endTimeStr); err == nil {
				if device.LastHeartbeatTime.After(endTime) {
					continue
				}
			}
		}

		filtered = append(filtered, device)
	}

	return filtered
}

// calculateStats 计算设备统计信息
func calculateStats(devices []*tdengine.DeviceStatus) *DeviceStats {
	total := len(devices)
	online := 0
	offline := 0
	error := 0

	for _, device := range devices {
		if device.CurrentStatus == 1 {
			online++
		} else {
			offline++
		}

		if device.ErrorCount > 0 {
			error++
		}
	}

	onlineRate := 0
	offlineRate := 0
	if total > 0 {
		onlineRate = (online * 100) / total
		offlineRate = (offline * 100) / total
	}

	return &DeviceStats{
		TotalDevices:   total,
		OnlineDevices:  online,
		OfflineDevices: offline,
		ErrorDevices:   error,
		OnlineRate:     onlineRate,
		OfflineRate:    offlineRate,
	}
}

// RegisterRoutes 注册路由
func RegisterDeviceStatusRoutes(s *ghttp.Server) {
	controller := NewDeviceStatusController()

	// API路由组
	api := s.Group("/api/v1")

	// 设置CORS中间件（只应用到API路由）
	api.Middleware(func(r *ghttp.Request) {
		r.Response.CORSDefault()
		r.Middleware.Next()
	})

	// 设备状态相关路由
	devices := api.Group("/devices")
	devices.GET("/", controller.GetDeviceList)
	devices.GET("/list", controller.GetDeviceList)
	devices.GET("/:imsi", controller.GetDeviceDetail)
	devices.GET("/:imsi/history", controller.GetDeviceHistory)

	// 健康检查
	api.GET("/health", func(r *ghttp.Request) {
		r.Response.WriteJsonExit(APIResponse{
			Code:    200,
			Message: "OK",
			Data: map[string]interface{}{
				"status":    "healthy",
				"timestamp": time.Now().Unix(),
				"version":   "1.0.0",
			},
		})
	})

	pix_log.Info("[API] 设备状态API路由注册完成")
}
