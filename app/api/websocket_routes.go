package api

import (
	"fmt"
	websocketService "ccserver/app/websocket"
	"ccserver/pix_log"
	"net/http"

	"github.com/gogf/gf/net/ghttp"
	"github.com/gorilla/websocket"
)

// WebSocket升级器配置
var upgrader = websocket.Upgrader{
	ReadBufferSize:  1024,
	WriteBufferSize: 1024,
	CheckOrigin: func(r *http.Request) bool {
		// 允许所有来源的连接（生产环境中应该更严格）
		return true
	},
}

// WebSocketHandler WebSocket连接处理器
func WebSocketHandler(r *ghttp.Request) {
	fmt.Println("========== WebSocket连接请求 ==========")
	pix_log.Info("[WEBSOCKET] ========== 收到WebSocket连接请求 ==========")
	pix_log.Info("[WEBSOCKET] 客户端IP: %s", r.<PERSON>ClientIp())
	pix_log.Info("[WEBSOCKET] 请求URL: %s", r.Request.URL.String())

	// 升级HTTP连接为WebSocket连接
	pix_log.Info("[WEBSOCKET] 开始升级HTTP连接为WebSocket连接")
	conn, err := upgrader.Upgrade(r.Response.ResponseWriter, r.Request, nil)
	if err != nil {
		pix_log.Error("[WEBSOCKET] WebSocket升级失败: %v", err)
		r.Response.WriteStatus(http.StatusBadRequest)
		return
	}

	pix_log.Info("[WEBSOCKET] WebSocket连接升级成功")

	// 确保WebSocket服务已初始化
	pix_log.Info("[WEBSOCKET] 检查WebSocket服务是否已初始化")
	pix_log.Info("[WEBSOCKET] GlobalWebSocketService 地址: %p", websocketService.GlobalWebSocketService)
	if websocketService.GlobalWebSocketService == nil {
		pix_log.Error("[WEBSOCKET] WebSocket服务未初始化，关闭连接")
		conn.Close()
		return
	}
	pix_log.Info("[WEBSOCKET] WebSocket服务已初始化，继续处理连接")

	// 添加客户端连接
	pix_log.Info("[WEBSOCKET] 添加客户端连接到服务")
	websocketService.GlobalWebSocketService.AddClient(conn)
	pix_log.Info("[WEBSOCKET] 客户端连接添加成功")

	// 发送欢迎消息
	pix_log.Info("[WEBSOCKET] 发送欢迎消息")
	welcomeMsg := map[string]interface{}{
		"type":    "welcome",
		"message": "WebSocket连接成功",
		"server":  "ccserver",
		"version": "1.0.0",
	}
	err = conn.WriteJSON(welcomeMsg)
	if err != nil {
		pix_log.Error("[WEBSOCKET] 发送欢迎消息失败: %v", err)
	} else {
		pix_log.Info("[WEBSOCKET] 欢迎消息发送成功")
	}

	// 立即发送一次设备列表更新
	pix_log.Info("[WEBSOCKET] 启动设备列表更新goroutine")
	go websocketService.GlobalWebSocketService.SendDeviceListUpdate()

	// 处理客户端消息
	pix_log.Info("[WEBSOCKET] 启动客户端消息处理goroutine")
	go handleClientMessages(conn)
	pix_log.Info("[WEBSOCKET] ========== WebSocket连接处理完成 ==========")
}

// handleClientMessages 处理客户端消息
func handleClientMessages(conn *websocket.Conn) {
	pix_log.Info("[WEBSOCKET] 开始处理客户端消息")
	defer func() {
		pix_log.Info("[WEBSOCKET] 客户端消息处理结束，移除客户端连接")
		// 连接关闭时移除客户端
		if websocketService.GlobalWebSocketService != nil {
			websocketService.GlobalWebSocketService.RemoveClient(conn)
		}
	}()

	for {
		// 读取客户端消息
		pix_log.Info("[WEBSOCKET] 等待客户端消息...")
		messageType, data, err := conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				pix_log.Error("[WEBSOCKET] WebSocket连接异常关闭: %v", err)
			} else {
				pix_log.Info("[WEBSOCKET] WebSocket连接正常关闭")
			}
			break
		}

		pix_log.Info("[WEBSOCKET] 收到客户端消息，类型: %d，数据长度: %d", messageType, len(data))
		pix_log.Info("[WEBSOCKET] 消息内容: %s", string(data))

		// 处理消息
		if websocketService.GlobalWebSocketService != nil {
			pix_log.Info("[WEBSOCKET] 调用HandleMessage处理消息")
			websocketService.GlobalWebSocketService.HandleMessage(conn, messageType, data)
		} else {
			pix_log.Error("[WEBSOCKET] GlobalWebSocketService为nil，无法处理消息")
		}
	}
}

// GetWebSocketStats 获取WebSocket统计信息
func GetWebSocketStats(r *ghttp.Request) {
	stats := map[string]interface{}{
		"connected_clients": 0,
		"service_status":    "stopped",
	}

	if websocketService.GlobalWebSocketService != nil {
		stats["connected_clients"] = websocketService.GlobalWebSocketService.GetClientCount()
		stats["service_status"] = "running"
	}

	r.Response.WriteJsonExit(APIResponse{
		Code:    200,
		Message: "success",
		Data:    stats,
	})
}

// RegisterWebSocketRoutes 注册WebSocket路由
func RegisterWebSocketRoutes(s *ghttp.Server) {
	pix_log.Info("[WEBSOCKET] 注册WebSocket路由")

	// WebSocket连接端点
	s.BindHandler("/ws", WebSocketHandler)
	
	// WebSocket统计信息端点
	s.BindHandler("/api/v1/websocket/stats", GetWebSocketStats)

	pix_log.Info("[WEBSOCKET] WebSocket路由注册完成")
	pix_log.Info("[WEBSOCKET] WebSocket端点: ws://localhost:8080/ws")
	pix_log.Info("[WEBSOCKET] 统计信息端点: http://localhost:8080/api/v1/websocket/stats")
}
